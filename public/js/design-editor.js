// Design Editor JavaScript
class DesignEditor {
    constructor() {
        this.canvas = document.getElementById('designCanvas');
        this.selectedElement = null;
        this.isDragging = false;
        this.isResizing = false;
        this.dragOffset = { x: 0, y: 0 };
        this.zoomLevel = 1;
        this.history = [];
        this.historyIndex = -1;
        this.currentBusiness = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadDefaultBusiness();
        this.saveState();
    }
    
    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.sidebar-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
        });
        
        // Business selector
        document.getElementById('businessSelect').addEventListener('change', (e) => {
            this.loadBusiness(e.target.value);
        });
        
        // Canvas events
        this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.addEventListener('mouseup', () => this.handleMouseUp());
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Undo/Redo buttons
        document.getElementById('undoBtn').addEventListener('click', () => this.undo());
        document.getElementById('redoBtn').addEventListener('click', () => this.redo());
        
        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    switchTab(tabId) {
        // Update tab buttons
        document.querySelectorAll('.sidebar-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabId);
        });
        
        // Update tab panels
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.toggle('active', panel.id === tabId);
        });
    }
    
    async loadBusiness(businessId) {
        try {
            const response = await fetch(`/design/business/${businessId}`, {
                headers: {
                    'X-CSRF-TOKEN': window.editorData.csrfToken
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                this.currentBusiness = result.data;
            }
        } catch (error) {
            console.error('Failed to load business:', error);
        }
    }
    
    loadDefaultBusiness() {
        this.currentBusiness = window.editorData.defaultBusiness;
    }
    
    addElement(type) {
        if (!this.currentBusiness && type.startsWith('business-')) {
            alert('Please select a business profile first');
            return;
        }
        
        const element = this.createElement(type);
        this.canvas.appendChild(element);
        this.selectElement(element);
        this.saveState();
    }
    
    createElement(type) {
        const element = document.createElement('div');
        element.className = 'design-element';
        element.dataset.type = type;
        element.dataset.id = Date.now().toString();
        
        // Set initial position
        const rect = this.canvas.getBoundingClientRect();
        element.style.left = '50px';
        element.style.top = '50px';
        
        // Create content based on type
        let content = '';
        switch (type) {
            case 'business-name':
                content = `<div class="element-text" style="font-size: 24px; font-weight: bold;">${this.currentBusiness?.name || 'Business Name'}</div>`;
                break;
            case 'business-slogan':
                content = `<div class="element-text" style="font-size: 16px; font-style: italic;">${this.currentBusiness?.slogan || 'Your Slogan Here'}</div>`;
                break;
            case 'business-phone':
                content = `<div class="element-text" style="font-size: 14px;"><i class="fas fa-phone"></i> ${this.currentBusiness?.phone || '+****************'}</div>`;
                break;
            case 'business-email':
                content = `<div class="element-text" style="font-size: 14px;"><i class="fas fa-envelope"></i> ${this.currentBusiness?.email || '<EMAIL>'}</div>`;
                break;
            case 'business-website':
                content = `<div class="element-text" style="font-size: 14px;"><i class="fas fa-globe"></i> ${this.currentBusiness?.website || 'www.business.com'}</div>`;
                break;
            case 'business-address':
                content = `<div class="element-text" style="font-size: 12px;"><i class="fas fa-map-marker-alt"></i> ${this.currentBusiness?.address || 'Business Address'}</div>`;
                break;
            case 'business-logo':
                if (this.currentBusiness?.logo_url) {
                    content = `<img src="${this.currentBusiness.logo_url}" class="element-logo" alt="Business Logo">`;
                } else {
                    content = `<div class="element-logo" style="display: flex; align-items: center; justify-content: center; background: #f1f5f9; color: #64748b;"><i class="fas fa-building fa-2x"></i></div>`;
                }
                break;
            case 'custom-text':
                content = `<div class="element-text" contenteditable="true" style="font-size: 16px;">Custom Text</div>`;
                break;
        }
        
        element.innerHTML = content + '<div class="resize-handle se"></div>';
        
        // Make element draggable
        element.draggable = false; // We'll handle dragging manually
        
        return element;
    }
    
    selectElement(element) {
        // Remove selection from other elements
        document.querySelectorAll('.design-element').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Select the new element
        element.classList.add('selected');
        this.selectedElement = element;
        
        // Update properties panel
        this.updatePropertiesPanel(element);
    }
    
    updatePropertiesPanel(element) {
        const propertiesContent = document.getElementById('propertiesContent');
        const type = element.dataset.type;
        
        let html = `
            <div class="properties-panel">
                <h4 style="margin-bottom: 1rem;">Element Properties</h4>
                
                <div class="property-group">
                    <label class="property-label">Position</label>
                    <div class="property-row">
                        <input type="number" class="property-input" id="posX" value="${parseInt(element.style.left)}" placeholder="X">
                        <input type="number" class="property-input" id="posY" value="${parseInt(element.style.top)}" placeholder="Y">
                    </div>
                </div>
        `;
        
        if (type !== 'business-logo') {
            const textElement = element.querySelector('.element-text');
            const computedStyle = window.getComputedStyle(textElement);
            
            html += `
                <div class="property-group">
                    <label class="property-label">Font Size</label>
                    <input type="number" class="property-input" id="fontSize" value="${parseInt(computedStyle.fontSize)}" min="8" max="72">
                </div>
                
                <div class="property-group">
                    <label class="property-label">Text Color</label>
                    <input type="color" class="color-input" id="textColor" value="${this.rgbToHex(computedStyle.color)}">
                </div>
                
                <div class="property-group">
                    <label class="property-label">Background Color</label>
                    <input type="color" class="color-input" id="bgColor" value="#ffffff">
                </div>
                
                <div class="property-group">
                    <label class="property-label">Font Weight</label>
                    <select class="property-input" id="fontWeight">
                        <option value="normal" ${computedStyle.fontWeight === 'normal' ? 'selected' : ''}>Normal</option>
                        <option value="bold" ${computedStyle.fontWeight === 'bold' || computedStyle.fontWeight === '700' ? 'selected' : ''}>Bold</option>
                    </select>
                </div>
            `;
        }
        
        html += `
                <div class="property-group">
                    <button class="btn btn-outline" onclick="editor.deleteElement()" style="width: 100%; color: #dc2626; border-color: #dc2626;">
                        <i class="fas fa-trash"></i> Delete Element
                    </button>
                </div>
            </div>
        `;
        
        propertiesContent.innerHTML = html;
        
        // Add event listeners for property changes
        this.setupPropertyListeners();
    }
    
    setupPropertyListeners() {
        const posX = document.getElementById('posX');
        const posY = document.getElementById('posY');
        const fontSize = document.getElementById('fontSize');
        const textColor = document.getElementById('textColor');
        const bgColor = document.getElementById('bgColor');
        const fontWeight = document.getElementById('fontWeight');
        
        if (posX) posX.addEventListener('change', () => this.updateElementPosition());
        if (posY) posY.addEventListener('change', () => this.updateElementPosition());
        if (fontSize) fontSize.addEventListener('change', () => this.updateElementStyle());
        if (textColor) textColor.addEventListener('change', () => this.updateElementStyle());
        if (bgColor) bgColor.addEventListener('change', () => this.updateElementStyle());
        if (fontWeight) fontWeight.addEventListener('change', () => this.updateElementStyle());
    }
    
    updateElementPosition() {
        if (!this.selectedElement) return;
        
        const posX = document.getElementById('posX').value;
        const posY = document.getElementById('posY').value;
        
        this.selectedElement.style.left = posX + 'px';
        this.selectedElement.style.top = posY + 'px';
        
        this.saveState();
    }
    
    updateElementStyle() {
        if (!this.selectedElement) return;
        
        const textElement = this.selectedElement.querySelector('.element-text');
        if (!textElement) return;
        
        const fontSize = document.getElementById('fontSize')?.value;
        const textColor = document.getElementById('textColor')?.value;
        const bgColor = document.getElementById('bgColor')?.value;
        const fontWeight = document.getElementById('fontWeight')?.value;
        
        if (fontSize) textElement.style.fontSize = fontSize + 'px';
        if (textColor) textElement.style.color = textColor;
        if (bgColor) textElement.style.backgroundColor = bgColor;
        if (fontWeight) textElement.style.fontWeight = fontWeight;
        
        this.saveState();
    }
    
    deleteElement() {
        if (this.selectedElement) {
            this.selectedElement.remove();
            this.selectedElement = null;
            document.getElementById('propertiesContent').innerHTML = '<p style="color: #64748b; text-align: center; padding: 2rem;">Select an element to edit its properties</p>';
            this.saveState();
        }
    }
    
    handleCanvasClick(e) {
        if (e.target === this.canvas) {
            // Clicked on empty canvas
            this.selectElement(null);
        } else if (e.target.closest('.design-element')) {
            // Clicked on an element
            this.selectElement(e.target.closest('.design-element'));
        }
    }
    
    handleMouseDown(e) {
        const element = e.target.closest('.design-element');
        if (!element) return;
        
        this.isDragging = true;
        this.selectElement(element);
        
        const rect = element.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();
        
        this.dragOffset = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        element.classList.add('dragging');
        e.preventDefault();
    }
    
    handleMouseMove(e) {
        if (!this.isDragging || !this.selectedElement) return;
        
        const canvasRect = this.canvas.getBoundingClientRect();
        const x = e.clientX - canvasRect.left - this.dragOffset.x;
        const y = e.clientY - canvasRect.top - this.dragOffset.y;
        
        // Keep element within canvas bounds
        const maxX = this.canvas.offsetWidth - this.selectedElement.offsetWidth;
        const maxY = this.canvas.offsetHeight - this.selectedElement.offsetHeight;
        
        const clampedX = Math.max(0, Math.min(x, maxX));
        const clampedY = Math.max(0, Math.min(y, maxY));
        
        this.selectedElement.style.left = clampedX + 'px';
        this.selectedElement.style.top = clampedY + 'px';
        
        // Update properties panel if open
        const posX = document.getElementById('posX');
        const posY = document.getElementById('posY');
        if (posX) posX.value = clampedX;
        if (posY) posY.value = clampedY;
    }
    
    handleMouseUp() {
        if (this.isDragging) {
            this.isDragging = false;
            if (this.selectedElement) {
                this.selectedElement.classList.remove('dragging');
                this.saveState();
            }
        }
    }
    
    handleKeyDown(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'z':
                    e.preventDefault();
                    if (e.shiftKey) {
                        this.redo();
                    } else {
                        this.undo();
                    }
                    break;
                case 'y':
                    e.preventDefault();
                    this.redo();
                    break;
            }
        }
        
        if (e.key === 'Delete' && this.selectedElement) {
            this.deleteElement();
        }
    }
    
    saveState() {
        const state = {
            elements: Array.from(this.canvas.querySelectorAll('.design-element')).map(el => ({
                id: el.dataset.id,
                type: el.dataset.type,
                style: el.style.cssText,
                innerHTML: el.innerHTML
            }))
        };
        
        // Remove future history if we're not at the end
        this.history = this.history.slice(0, this.historyIndex + 1);
        this.history.push(JSON.stringify(state));
        this.historyIndex++;
        
        // Limit history size
        if (this.history.length > 50) {
            this.history.shift();
            this.historyIndex--;
        }
        
        this.updateHistoryButtons();
    }
    
    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.restoreState();
        }
    }
    
    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.restoreState();
        }
    }
    
    restoreState() {
        const state = JSON.parse(this.history[this.historyIndex]);
        
        // Clear canvas
        this.canvas.querySelectorAll('.design-element').forEach(el => el.remove());
        
        // Restore elements
        state.elements.forEach(elementData => {
            const element = document.createElement('div');
            element.className = 'design-element';
            element.dataset.id = elementData.id;
            element.dataset.type = elementData.type;
            element.style.cssText = elementData.style;
            element.innerHTML = elementData.innerHTML;
            this.canvas.appendChild(element);
        });
        
        this.selectedElement = null;
        this.updateHistoryButtons();
    }
    
    updateHistoryButtons() {
        document.getElementById('undoBtn').disabled = this.historyIndex <= 0;
        document.getElementById('redoBtn').disabled = this.historyIndex >= this.history.length - 1;
    }
    
    rgbToHex(rgb) {
        const result = rgb.match(/\d+/g);
        if (!result) return '#000000';
        return '#' + result.map(x => parseInt(x).toString(16).padStart(2, '0')).join('');
    }
}

// Global functions
function addElement(type) {
    window.editor.addElement(type);
}

function changeFrame(frameId) {
    // Implementation for changing frame
    console.log('Change frame to:', frameId);
}

function zoomIn() {
    window.editor.zoomLevel = Math.min(window.editor.zoomLevel + 0.1, 2);
    updateZoom();
}

function zoomOut() {
    window.editor.zoomLevel = Math.max(window.editor.zoomLevel - 0.1, 0.5);
    updateZoom();
}

function updateZoom() {
    const canvas = document.getElementById('designCanvas');
    canvas.style.transform = `scale(${window.editor.zoomLevel})`;
    document.getElementById('zoomLevel').textContent = Math.round(window.editor.zoomLevel * 100) + '%';
}

async function saveDesign() {
    const designData = {
        elements: Array.from(document.querySelectorAll('.design-element')).map(el => ({
            id: el.dataset.id,
            type: el.dataset.type,
            style: el.style.cssText,
            innerHTML: el.innerHTML
        })),
        template_id: window.editorData.template?.id,
        template_type: window.editorData.type,
        business_id: document.getElementById('businessSelect').value
    };
    
    try {
        const response = await fetch('/design/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.editorData.csrfToken
            },
            body: JSON.stringify({
                design_data: JSON.stringify(designData),
                template_id: designData.template_id,
                template_type: designData.template_type,
                business_id: designData.business_id
            })
        });
        
        const result = await response.json();
        if (result.status === 'success') {
            alert('Design saved successfully!');
        } else {
            alert('Failed to save design: ' + result.message);
        }
    } catch (error) {
        console.error('Save error:', error);
        alert('Failed to save design');
    }
}

async function downloadDesign() {
    const designData = {
        elements: Array.from(document.querySelectorAll('.design-element')).map(el => ({
            id: el.dataset.id,
            type: el.dataset.type,
            style: el.style.cssText,
            innerHTML: el.innerHTML
        })),
        template_id: window.editorData.template?.id,
        template_type: window.editorData.type,
        business_id: document.getElementById('businessSelect').value
    };

    try {
        // First check download eligibility
        const eligibilityResponse = await fetch('/payment/download/check', {
            headers: {
                'X-CSRF-TOKEN': window.editorData.csrfToken
            }
        });

        const eligibility = await eligibilityResponse.json();

        if (eligibility.status === 'success') {
            const options = eligibility.eligibility;

            if (options.can_download_subscription && options.has_active_subscription) {
                // User has active subscription - direct download
                processDownload(designData);
            } else if (options.can_download_free) {
                // Show download options modal
                showDownloadOptionsModal(designData, options);
            } else if (options.can_download_paid) {
                // Only paid download available
                initiatePaymentDownload(designData, options.download_cost);
            } else {
                alert('Downloads are currently disabled. Please contact support.');
            }
        }
    } catch (error) {
        console.error('Download error:', error);
        alert('Failed to check download eligibility');
    }
}

function showDownloadOptionsModal(designData, options) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed; top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(0,0,0,0.5); z-index: 1000;
        display: flex; align-items: center; justify-content: center;
    `;

    modal.innerHTML = `
        <div style="background: white; border-radius: 12px; padding: 2rem; max-width: 400px; width: 90%;">
            <h3 style="margin-bottom: 1rem; text-align: center;">Choose Download Option</h3>

            ${options.can_download_free ? `
                <button onclick="processDownload(${JSON.stringify(designData).replace(/"/g, '&quot;')}); closeModal()"
                        style="width: 100%; padding: 12px; margin-bottom: 1rem; background: #10b981; color: white; border: none; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-gift"></i> Free Download
                </button>
            ` : ''}

            ${options.can_download_paid ? `
                <button onclick="initiatePaymentDownload(${JSON.stringify(designData).replace(/"/g, '&quot;')}, '${options.download_cost}'); closeModal()"
                        style="width: 100%; padding: 12px; margin-bottom: 1rem; background: var(--primary-color); color: white; border: none; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-credit-card"></i> Pay ₹${options.download_cost} & Download
                </button>
            ` : ''}

            ${options.can_download_subscription ? `
                <a href="/payment/plans"
                   style="display: block; width: 100%; padding: 12px; margin-bottom: 1rem; background: #f59e0b; color: white; border: none; border-radius: 6px; text-decoration: none; text-align: center;">
                    <i class="fas fa-crown"></i> Get Subscription
                </a>
            ` : ''}

            <button onclick="closeModal()"
                    style="width: 100%; padding: 8px; background: #e5e7eb; color: #374151; border: none; border-radius: 6px; cursor: pointer;">
                Cancel
            </button>
        </div>
    `;

    document.body.appendChild(modal);
    window.currentModal = modal;
}

function closeModal() {
    if (window.currentModal) {
        document.body.removeChild(window.currentModal);
        window.currentModal = null;
    }
}

async function processDownload(designData) {
    try {
        const response = await fetch('/design/download', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.editorData.csrfToken
            },
            body: JSON.stringify({
                design_data: JSON.stringify(designData),
                template_id: designData.template_id,
                template_type: designData.template_type,
                business_id: designData.business_id,
                format: 'png',
                quality: 90
            })
        });

        const result = await response.json();
        alert(result.message);
    } catch (error) {
        console.error('Download error:', error);
        alert('Failed to download design');
    }
}

async function initiatePaymentDownload(designData, cost) {
    try {
        // Create payment order
        const response = await fetch('/payment/download/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.editorData.csrfToken
            },
            body: JSON.stringify({
                design_data: JSON.stringify(designData),
                template_id: designData.template_id,
                template_type: designData.template_type
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            // Open Razorpay checkout
            const options = {
                key: result.key,
                amount: result.amount,
                currency: result.currency,
                name: 'Social Media Post Creator',
                description: 'Download Payment',
                order_id: result.order_id,
                handler: function(response) {
                    verifyDownloadPayment(response, designData);
                },
                theme: {
                    color: getComputedStyle(document.documentElement).getPropertyValue('--primary-color')
                }
            };

            const rzp = new Razorpay(options);
            rzp.open();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        console.error('Payment error:', error);
        alert('Failed to initiate payment');
    }
}

async function verifyDownloadPayment(response, designData) {
    try {
        const verifyResponse = await fetch('/payment/download/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.editorData.csrfToken
            },
            body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature,
                design_data: JSON.stringify(designData)
            })
        });

        const result = await verifyResponse.json();

        if (result.status === 'success') {
            alert(result.message);
            // Process the actual download
            processDownload(designData);
        } else {
            alert('Payment verification failed: ' + result.message);
        }
    } catch (error) {
        console.error('Verification error:', error);
        alert('Payment verification failed');
    }
}

// Initialize editor when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.editor = new DesignEditor();
});
