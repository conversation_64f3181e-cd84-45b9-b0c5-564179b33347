@extends('layouts.user')

@section('title', 'Home - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Create stunning social media posts with our easy-to-use design tools and templates')

@push('styles')
<style>
    .hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        margin-bottom: 4rem;
    }
    
    .hero-content h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: white;
    }
    
    .hero-content p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }
    
    .hero-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-hero {
        padding: 16px 32px;
        font-size: 18px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .btn-hero-primary {
        background: white;
        color: var(--primary-color);
    }
    
    .btn-hero-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .btn-hero-secondary {
        background: transparent;
        color: white;
        border: 2px solid white;
    }
    
    .btn-hero-secondary:hover {
        background: white;
        color: var(--primary-color);
    }
    
    .section {
        margin-bottom: 4rem;
    }
    
    .section-header {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .section-header h2 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .section-header p {
        font-size: 1.125rem;
        color: #64748b;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .category-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
    }
    
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    }
    
    .category-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }
    
    .category-card h3 {
        margin-bottom: 0.5rem;
        color: #1e293b;
    }
    
    .category-card p {
        color: #64748b;
        font-size: 0.9rem;
    }
    
    .posters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    
    .poster-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .poster-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .poster-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        background: #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
    }
    
    .poster-content {
        padding: 1rem;
    }
    
    .poster-content h4 {
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }
    
    .poster-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.875rem;
        color: #64748b;
    }
    
    .premium-badge {
        background: linear-gradient(45deg, #f59e0b, #f97316);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .features-section {
        background: #f8fafc;
        padding: 4rem 0;
        margin: 4rem 0;
    }
    
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }
    
    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        background: var(--primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 1.5rem;
    }
    
    .feature-card h3 {
        margin-bottom: 1rem;
    }
    
    .cta-section {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        border-radius: 20px;
        margin: 4rem 0;
    }
    
    .cta-section h2 {
        color: white;
        margin-bottom: 1rem;
    }
    
    .cta-section p {
        font-size: 1.125rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }
    
    @media (max-width: 768px) {
        .hero-content h1 {
            font-size: 2rem;
        }
        
        .hero-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .btn-hero {
            width: 100%;
            max-width: 300px;
        }
        
        .categories-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .posters-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }
    }
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1>{{ $branding['application_name'] ?? 'Social Media Post Creator' }}</h1>
            <p>{{ $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly' }}</p>
            <div class="hero-buttons">
                @auth
                    <a href="{{ route('user.categories') }}" class="btn-hero btn-hero-primary">
                        <i class="fas fa-palette"></i> Start Creating
                    </a>
                    <a href="{{ route('user.dashboard') }}" class="btn-hero btn-hero-secondary">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                @else
                    <a href="{{ route('user.register') }}" class="btn-hero btn-hero-primary">
                        <i class="fas fa-user-plus"></i> Get Started Free
                    </a>
                    <a href="{{ route('user.login') }}" class="btn-hero btn-hero-secondary">
                        <i class="fas fa-sign-in-alt"></i> Sign In
                    </a>
                @endauth
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
@if($categories->count() > 0)
<section class="section">
    <div class="container">
        <div class="section-header">
            <h2>Popular Categories</h2>
            <p>Choose from our wide range of design categories to get started</p>
        </div>
        
        <div class="categories-grid">
            @foreach($categories as $category)
                <a href="{{ route('user.categories.show', $category) }}" class="category-card">
                    @if($category->icon)
                        <span class="category-icon">{{ $category->icon }}</span>
                    @else
                        <span class="category-icon">🎨</span>
                    @endif
                    <h3>{{ $category->name }}</h3>
                    @if($category->description)
                        <p>{{ Str::limit($category->description, 80) }}</p>
                    @endif
                </a>
            @endforeach
        </div>
        
        <div style="text-align: center; margin-top: 2rem;">
            <a href="{{ route('user.categories') }}" class="btn btn-outline">
                View All Categories
            </a>
        </div>
    </div>
</section>
@endif

<!-- Recent Posters Section -->
@if($recentPosters->count() > 0)
<section class="section">
    <div class="container">
        <div class="section-header">
            <h2>Latest Templates</h2>
            <p>Fresh designs added regularly to keep your content looking modern</p>
        </div>
        
        <div class="posters-grid">
            @foreach($recentPosters as $poster)
                <div class="poster-card">
                    @if($poster->image_path)
                        <img src="{{ Storage::url($poster->image_path) }}" alt="{{ $poster->name }}" class="poster-image">
                    @else
                        <div class="poster-image">
                            <i class="fas fa-image fa-2x"></i>
                        </div>
                    @endif
                    <div class="poster-content">
                        <h4>{{ $poster->name }}</h4>
                        <div class="poster-meta">
                            @if($poster->category)
                                <span>{{ $poster->category->name }}</span>
                            @endif
                            @if($poster->is_premium)
                                <span class="premium-badge">Premium</span>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="section-header">
            <h2>Why Choose Us?</h2>
            <p>Everything you need to create professional social media content</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <h3>Easy to Use</h3>
                <p>Intuitive drag-and-drop editor makes design simple for everyone</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-images"></i>
                </div>
                <h3>Thousands of Templates</h3>
                <p>Choose from our vast library of professionally designed templates</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3>Mobile Optimized</h3>
                <p>Create and edit designs on any device, anywhere, anytime</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
@guest
<section class="cta-section">
    <div class="container">
        <h2>Ready to Start Creating?</h2>
        <p>Join thousands of users who are already creating amazing content</p>
        <div class="hero-buttons">
            <a href="{{ route('user.register') }}" class="btn-hero btn-hero-primary">
                <i class="fas fa-rocket"></i> Get Started Now
            </a>
        </div>
    </div>
</section>
@endguest
@endsection
