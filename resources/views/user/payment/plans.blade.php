@extends('layouts.user')

@section('title', 'Subscription Plans - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Choose the perfect plan for your design needs')

@push('styles')
<style>
    .plans-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .plans-header h1 {
        color: white;
        margin-bottom: 1rem;
    }
    
    .plans-header p {
        font-size: 1.125rem;
        opacity: 0.9;
    }
    
    .current-plan-alert {
        background: #dcfce7;
        color: #166534;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
        border: 1px solid #bbf7d0;
    }
    
    .plans-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .plan-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 2px solid transparent;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .plan-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }
    
    .plan-card.featured {
        border-color: var(--primary-color);
        transform: scale(1.05);
    }
    
    .plan-card.featured::before {
        content: 'Most Popular';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        background: var(--primary-color);
        color: white;
        text-align: center;
        padding: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .plan-card.current {
        border-color: #10b981;
        background: #f0fdf4;
    }
    
    .plan-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-top: 1rem;
    }
    
    .plan-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
    
    .plan-price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        gap: 0.25rem;
        margin-bottom: 0.5rem;
    }
    
    .price-currency {
        font-size: 1.25rem;
        color: #64748b;
    }
    
    .price-amount {
        font-size: 3rem;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .price-period {
        font-size: 1rem;
        color: #64748b;
    }
    
    .plan-duration {
        color: #64748b;
        font-size: 0.875rem;
    }
    
    .plan-features {
        list-style: none;
        margin-bottom: 2rem;
    }
    
    .plan-features li {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0;
        color: #374151;
    }
    
    .feature-icon {
        width: 20px;
        height: 20px;
        background: #dcfce7;
        color: #166534;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        flex-shrink: 0;
    }
    
    .feature-icon.unavailable {
        background: #fef2f2;
        color: #dc2626;
    }
    
    .plan-action {
        text-align: center;
    }
    
    .btn-plan {
        width: 100%;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid;
        font-size: 16px;
    }
    
    .btn-plan-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
    
    .btn-plan-primary:hover {
        background: color-mix(in srgb, var(--primary-color) 85%, black);
        transform: translateY(-1px);
    }
    
    .btn-plan-outline {
        background: transparent;
        color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .btn-plan-outline:hover {
        background: var(--primary-color);
        color: white;
    }
    
    .btn-plan-current {
        background: #10b981;
        color: white;
        border-color: #10b981;
        cursor: default;
    }
    
    .payment-methods {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .payment-methods h3 {
        margin-bottom: 1rem;
        color: #1e293b;
    }
    
    .payment-icons {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .payment-icon {
        width: 60px;
        height: 40px;
        background: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
    }
    
    .download-options {
        background: #f8fafc;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .download-options h3 {
        text-align: center;
        margin-bottom: 1.5rem;
        color: #1e293b;
    }
    
    .download-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .download-option {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        border: 2px solid #e5e7eb;
        transition: all 0.2s ease;
    }
    
    .download-option.enabled {
        border-color: var(--primary-color);
        background: #f0f9ff;
    }
    
    .download-option.disabled {
        opacity: 0.5;
        background: #f1f5f9;
    }
    
    .option-icon {
        width: 50px;
        height: 50px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.25rem;
    }
    
    .option-title {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
    
    .option-desc {
        color: #64748b;
        font-size: 0.875rem;
    }
    
    @media (max-width: 768px) {
        .plans-grid {
            grid-template-columns: 1fr;
        }
        
        .plan-card.featured {
            transform: none;
        }
        
        .payment-icons {
            gap: 0.5rem;
        }
        
        .payment-icon {
            width: 50px;
            height: 35px;
        }
    }
</style>
@endpush

@section('content')
<!-- Plans Header -->
<section class="plans-header">
    <div class="container">
        <h1>Choose Your Plan</h1>
        <p>Select the perfect plan for your design needs</p>
    </div>
</section>

<div class="container">
    <!-- Current Plan Alert -->
    @if($currentSubscription)
        <div class="current-plan-alert">
            <i class="fas fa-check-circle"></i>
            You currently have an active <strong>{{ $currentSubscription->plan->name }}</strong> subscription 
            that expires on {{ $currentSubscription->end_date->format('F j, Y') }}.
        </div>
    @endif

    <!-- Download Options -->
    <div class="download-options">
        <h3>Available Download Options</h3>
        <div class="download-grid">
            <div class="download-option {{ $settings['free_download_enabled'] ? 'enabled' : 'disabled' }}">
                <div class="option-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="option-title">Free Downloads</div>
                <div class="option-desc">
                    @if($settings['free_download_enabled'])
                        Download designs for free with basic features
                    @else
                        Currently disabled
                    @endif
                </div>
            </div>
            
            <div class="download-option {{ $settings['pay_per_download_enabled'] ? 'enabled' : 'disabled' }}">
                <div class="option-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="option-title">Pay Per Download</div>
                <div class="option-desc">
                    @if($settings['pay_per_download_enabled'])
                        Pay ₹{{ $settings['per_download_cost'] }} for each download
                    @else
                        Currently disabled
                    @endif
                </div>
            </div>
            
            <div class="download-option {{ $settings['subscription_enabled'] ? 'enabled' : 'disabled' }}">
                <div class="option-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="option-title">Subscription Plans</div>
                <div class="option-desc">
                    @if($settings['subscription_enabled'])
                        Unlimited downloads with premium features
                    @else
                        Currently disabled
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Plans -->
    @if($settings['subscription_enabled'] && $plans->count() > 0)
        <div class="plans-grid">
            @foreach($plans as $index => $plan)
                <div class="plan-card {{ $index === 1 ? 'featured' : '' }} {{ $currentSubscription && $currentSubscription->plan_id === $plan->id ? 'current' : '' }}">
                    <div class="plan-header">
                        <div class="plan-name">{{ $plan->name }}</div>
                        <div class="plan-price">
                            <span class="price-currency">₹</span>
                            <span class="price-amount">{{ number_format($plan->price, 0) }}</span>
                        </div>
                        <div class="plan-duration">{{ $plan->duration_days }} days</div>
                    </div>
                    
                    <ul class="plan-features">
                        <li>
                            <span class="feature-icon">
                                <i class="fas fa-check"></i>
                            </span>
                            Unlimited downloads
                        </li>
                        <li>
                            <span class="feature-icon">
                                <i class="fas fa-check"></i>
                            </span>
                            Premium templates access
                        </li>
                        <li>
                            <span class="feature-icon">
                                <i class="fas fa-check"></i>
                            </span>
                            High-quality exports
                        </li>
                        <li>
                            <span class="feature-icon">
                                <i class="fas fa-check"></i>
                            </span>
                            Priority support
                        </li>
                        <li>
                            <span class="feature-icon">
                                <i class="fas fa-check"></i>
                            </span>
                            Commercial usage rights
                        </li>
                        @if($plan->features)
                            @foreach(json_decode($plan->features, true) as $feature)
                                <li>
                                    <span class="feature-icon">
                                        <i class="fas fa-check"></i>
                                    </span>
                                    {{ $feature }}
                                </li>
                            @endforeach
                        @endif
                    </ul>
                    
                    <div class="plan-action">
                        @if($currentSubscription && $currentSubscription->plan_id === $plan->id)
                            <button class="btn-plan btn-plan-current" disabled>
                                <i class="fas fa-check"></i> Current Plan
                            </button>
                        @else
                            <button class="btn-plan {{ $index === 1 ? 'btn-plan-primary' : 'btn-plan-outline' }}" 
                                    onclick="subscribeToPlan({{ $plan->id }})">
                                @if($currentSubscription)
                                    Switch to {{ $plan->name }}
                                @else
                                    Get {{ $plan->name }}
                                @endif
                            </button>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <!-- Payment Methods -->
    <div class="payment-methods">
        <h3>Secure Payment Methods</h3>
        <div class="payment-icons">
            <div class="payment-icon">
                <i class="fab fa-cc-visa"></i>
            </div>
            <div class="payment-icon">
                <i class="fab fa-cc-mastercard"></i>
            </div>
            <div class="payment-icon">
                <i class="fas fa-university"></i>
            </div>
            <div class="payment-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <div class="payment-icon">
                <i class="fas fa-wallet"></i>
            </div>
        </div>
        <p style="margin-top: 1rem; color: #64748b; font-size: 0.875rem;">
            Powered by Razorpay - Your payments are secure and encrypted
        </p>
    </div>
</div>

<!-- Razorpay Checkout Script -->
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
@endsection

@push('scripts')
<script>
    async function subscribeToPlan(planId) {
        try {
            // Create order
            const response = await fetch('{{ route("user.payment.subscription.create") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ plan_id: planId })
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                // Open Razorpay checkout
                const options = {
                    key: result.key,
                    amount: result.amount,
                    currency: result.currency,
                    name: '{{ $branding["application_name"] ?? "Social Media Post Creator" }}',
                    description: 'Subscription: ' + result.plan.name,
                    order_id: result.order_id,
                    handler: function(response) {
                        verifySubscriptionPayment(response, planId);
                    },
                    prefill: {
                        name: '{{ Auth::user()->name }}',
                        email: '{{ Auth::user()->email }}'
                    },
                    theme: {
                        color: getComputedStyle(document.documentElement).getPropertyValue('--primary-color')
                    }
                };
                
                const rzp = new Razorpay(options);
                rzp.open();
            } else {
                alert('Error: ' + result.message);
            }
        } catch (error) {
            console.error('Payment error:', error);
            alert('Failed to initiate payment');
        }
    }
    
    async function verifySubscriptionPayment(response, planId) {
        try {
            const verifyResponse = await fetch('{{ route("user.payment.subscription.verify") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    razorpay_order_id: response.razorpay_order_id,
                    razorpay_payment_id: response.razorpay_payment_id,
                    razorpay_signature: response.razorpay_signature,
                    plan_id: planId
                })
            });
            
            const result = await verifyResponse.json();
            
            if (result.status === 'success') {
                alert(result.message);
                if (result.redirect) {
                    window.location.href = result.redirect;
                } else {
                    window.location.reload();
                }
            } else {
                alert('Payment verification failed: ' + result.message);
            }
        } catch (error) {
            console.error('Verification error:', error);
            alert('Payment verification failed');
        }
    }
</script>
@endpush
