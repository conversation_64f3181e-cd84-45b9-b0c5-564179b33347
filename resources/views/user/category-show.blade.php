@extends('layouts.user')

@section('title', $category->name . ' - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Browse ' . $category->name . ' templates and designs for your social media posts')

@push('styles')
<style>
    .category-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .category-header h1 {
        color: white;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .category-icon {
        font-size: 3rem;
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }
    
    .breadcrumb a {
        color: white;
        text-decoration: none;
    }
    
    .breadcrumb a:hover {
        text-decoration: underline;
    }
    
    .filters-section {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .filters-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 1rem;
    }
    
    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .filter-label {
        font-weight: 500;
        color: #374151;
        font-size: 0.9rem;
    }
    
    .filter-select, .filter-input {
        padding: 8px 12px;
        border: 2px solid #e5e7eb;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s ease;
    }
    
    .filter-select:focus, .filter-input:focus {
        outline: none;
        border-color: var(--primary-color);
    }
    
    .filter-buttons {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-filter {
        padding: 8px 16px;
        font-size: 14px;
        border-radius: 6px;
    }
    
    .subcategories-section {
        margin-bottom: 2rem;
    }
    
    .subcategories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .subcategory-chip {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        text-decoration: none;
        color: #374151;
        transition: all 0.2s ease;
    }
    
    .subcategory-chip:hover, .subcategory-chip.active {
        border-color: var(--primary-color);
        background: #f0f9ff;
        color: var(--primary-color);
    }
    
    .subcategory-chip i {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .content-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        border-bottom: 2px solid #e5e7eb;
    }
    
    .tab-button {
        padding: 1rem 2rem;
        background: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        color: #64748b;
        cursor: pointer;
        border-bottom: 3px solid transparent;
        transition: all 0.2s ease;
    }
    
    .tab-button.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
    }
    
    .tab-content {
        display: none;
    }
    
    .tab-content.active {
        display: block;
    }
    
    .templates-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .template-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }
    
    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }
    
    .template-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        background: #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
        position: relative;
    }
    
    .template-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .template-card:hover .template-overlay {
        opacity: 1;
    }
    
    .template-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .action-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .btn-preview {
        background: white;
        color: #374151;
    }
    
    .btn-use {
        background: var(--primary-color);
        color: white;
    }
    
    .template-content {
        padding: 1rem;
    }
    
    .template-title {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
    
    .template-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.875rem;
        color: #64748b;
    }
    
    .premium-badge {
        background: linear-gradient(45deg, #f59e0b, #f97316);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .frames-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .frame-card {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }
    
    .frame-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }
    
    .frame-image {
        width: 80px;
        height: 80px;
        margin: 0 auto 0.5rem;
        background: #f1f5f9;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
    }
    
    .frame-name {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
    }
    
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }
    
    .pagination {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    .page-link {
        padding: 8px 12px;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        text-decoration: none;
        color: #374151;
        transition: all 0.2s ease;
    }
    
    .page-link:hover, .page-link.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #64748b;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #cbd5e1;
    }
    
    @media (max-width: 768px) {
        .category-header h1 {
            flex-direction: column;
            text-align: center;
        }
        
        .filters-grid {
            grid-template-columns: 1fr;
        }
        
        .content-tabs {
            flex-direction: column;
            gap: 0;
        }
        
        .tab-button {
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            border-radius: 0;
        }
        
        .templates-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        }
    }
</style>
@endpush

@section('content')
<!-- Category Header -->
<section class="category-header">
    <div class="container">
        <div class="breadcrumb">
            <a href="{{ route('user.home') }}">Home</a>
            <i class="fas fa-chevron-right"></i>
            <a href="{{ route('user.categories') }}">Categories</a>
            <i class="fas fa-chevron-right"></i>
            <span>{{ $category->name }}</span>
        </div>
        
        <h1>
            <div class="category-icon">
                @if($category->icon)
                    {{ $category->icon }}
                @else
                    <i class="fas fa-folder"></i>
                @endif
            </div>
            <div>
                {{ $category->name }}
                @if($category->description)
                    <p style="font-size: 1.125rem; margin: 0; opacity: 0.9;">{{ $category->description }}</p>
                @endif
            </div>
        </h1>
    </div>
</section>

<div class="container">
    <!-- Subcategories (if parent category) -->
    @if($subcategories->count() > 0)
        <div class="subcategories-section">
            <h3 style="margin-bottom: 1rem;">Browse Subcategories</h3>
            <div class="subcategories-grid">
                <a href="{{ route('user.categories.show', $category) }}" class="subcategory-chip {{ request()->route('category')->id == $category->id ? 'active' : '' }}">
                    <i class="fas fa-th-large"></i>
                    All {{ $category->name }}
                </a>
                @foreach($subcategories as $subcategory)
                    <a href="{{ route('user.categories.show', $subcategory) }}" class="subcategory-chip">
                        @if($subcategory->icon)
                            {{ $subcategory->icon }}
                        @else
                            <i class="fas fa-folder"></i>
                        @endif
                        {{ $subcategory->name }}
                    </a>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Filters Section -->
    <div class="filters-section">
        <div class="filters-header">
            <h3 style="margin: 0;">Filter Templates</h3>
        </div>
        <form id="filterForm" class="filters-grid">
            <div class="filter-group">
                <label class="filter-label">Search</label>
                <input type="text" name="search" class="filter-input" placeholder="Search templates..." value="{{ request('search') }}">
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Type</label>
                <select name="is_premium" class="filter-select">
                    <option value="">All Templates</option>
                    <option value="0" {{ request('is_premium') === '0' ? 'selected' : '' }}>Free</option>
                    <option value="1" {{ request('is_premium') === '1' ? 'selected' : '' }}>Premium</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Sort By</label>
                <select name="sort" class="filter-select">
                    <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest First</option>
                    <option value="oldest" {{ request('sort') === 'oldest' ? 'selected' : '' }}>Oldest First</option>
                    <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name A-Z</option>
                </select>
            </div>
            
            <div class="filter-buttons">
                <button type="submit" class="btn btn-primary btn-filter">Apply Filters</button>
                <a href="{{ route('user.categories.show', $category) }}" class="btn btn-outline btn-filter">Clear</a>
            </div>
        </form>
    </div>

    <!-- Content Tabs -->
    <div class="content-tabs">
        <button class="tab-button active" data-tab="posters">
            <i class="fas fa-image"></i> Templates ({{ $posters->total() }})
        </button>
        <button class="tab-button" data-tab="frames">
            <i class="fas fa-border-style"></i> Frames ({{ $frames->count() }})
        </button>
    </div>

    <!-- Posters Tab -->
    <div id="posters" class="tab-content active">
        @if($posters->count() > 0)
            <div class="templates-grid">
                @foreach($posters as $poster)
                    <div class="template-card" onclick="selectTemplate('poster', {{ $poster->id }})">
                        @if($poster->image_path)
                            <img src="{{ Storage::url($poster->image_path) }}" alt="{{ $poster->name }}" class="template-image">
                        @else
                            <div class="template-image">
                                <i class="fas fa-image fa-3x"></i>
                            </div>
                        @endif
                        
                        <div class="template-overlay">
                            <div class="template-actions">
                                <button class="action-btn btn-preview" onclick="event.stopPropagation(); previewTemplate('poster', {{ $poster->id }})">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="action-btn btn-use">
                                    <i class="fas fa-edit"></i> Use Template
                                </button>
                            </div>
                        </div>
                        
                        <div class="template-content">
                            <div class="template-title">{{ $poster->name }}</div>
                            <div class="template-meta">
                                @if($poster->category)
                                    <span>{{ $poster->category->name }}</span>
                                @endif
                                @if($poster->is_premium)
                                    <span class="premium-badge">Premium</span>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($posters->hasPages())
                <div class="pagination-wrapper">
                    {{ $posters->links() }}
                </div>
            @endif
        @else
            <div class="empty-state">
                <i class="fas fa-image"></i>
                <h3>No Templates Found</h3>
                <p>No templates available in this category yet. Try adjusting your filters or check back later.</p>
            </div>
        @endif
    </div>

    <!-- Frames Tab -->
    <div id="frames" class="tab-content">
        @if($frames->count() > 0)
            <div class="frames-grid">
                @foreach($frames as $frame)
                    <div class="frame-card" onclick="selectFrame({{ $frame->id }})">
                        @if($frame->image_path)
                            <img src="{{ Storage::url($frame->image_path) }}" alt="{{ $frame->name }}" class="frame-image">
                        @else
                            <div class="frame-image">
                                <i class="fas fa-border-style fa-2x"></i>
                            </div>
                        @endif
                        <div class="frame-name">{{ $frame->name }}</div>
                        @if($frame->is_premium)
                            <div style="margin-top: 0.5rem;">
                                <span class="premium-badge">Premium</span>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        @else
            <div class="empty-state">
                <i class="fas fa-border-style"></i>
                <h3>No Frames Found</h3>
                <p>No frames available in this category yet.</p>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Tab functionality
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabId = this.dataset.tab;
                
                // Remove active class from all tabs
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // Filter form auto-submit
        const filterForm = document.getElementById('filterForm');
        const filterInputs = filterForm.querySelectorAll('input, select');
        
        filterInputs.forEach(input => {
            if (input.type !== 'submit') {
                input.addEventListener('change', function() {
                    // Auto-submit form when filters change (except search input)
                    if (this.name !== 'search') {
                        filterForm.submit();
                    }
                });
            }
        });
        
        // Search input with debounce
        const searchInput = filterForm.querySelector('input[name="search"]');
        let searchTimeout;
        
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    filterForm.submit();
                }, 500);
            });
        }
    });
    
    function selectTemplate(type, id) {
        // This will redirect to the design editor
        window.location.href = `{{ route('user.design.create') }}?type=${type}&id=${id}`;
    }
    
    function previewTemplate(type, id) {
        // Preview functionality - to be implemented
        console.log(`Preview ${type} ${id}`);
    }
    
    function selectFrame(id) {
        // Frame selection functionality - to be implemented
        console.log(`Select frame ${id}`);
    }
</script>
@endpush
