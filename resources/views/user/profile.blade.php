@extends('layouts.user')

@section('title', 'Profile - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Manage your account settings and profile information')

@push('styles')
<style>
    .profile-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .profile-header h1 {
        color: white;
        margin-bottom: 0.5rem;
    }
    
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .profile-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        background: #f8fafc;
    }
    
    .card-header h3 {
        margin: 0;
        color: #1e293b;
    }
    
    .card-content {
        padding: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
    }
    
    .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.2s ease;
    }
    
    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
    }
    
    .form-input.error {
        border-color: #dc2626;
    }
    
    .error-message {
        color: #dc2626;
        font-size: 14px;
        margin-top: 0.5rem;
    }
    
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
    }
    
    .profile-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .info-item {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .info-label {
        font-weight: 500;
        color: #64748b;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .info-value {
        color: #1e293b;
        font-size: 1rem;
    }
    
    .account-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 8px;
    }
    
    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }
    
    .stat-label {
        color: #64748b;
        font-size: 0.875rem;
    }
    
    .danger-zone {
        border: 2px solid #fecaca;
        border-radius: 8px;
        padding: 1.5rem;
        background: #fef2f2;
    }
    
    .danger-zone h4 {
        color: #dc2626;
        margin-bottom: 1rem;
    }
    
    .danger-zone p {
        color: #7f1d1d;
        margin-bottom: 1rem;
    }
    
    .btn-danger {
        background: #dc2626;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .btn-danger:hover {
        background: #b91c1c;
    }
    
    @media (max-width: 768px) {
        .profile-container {
            margin: 0 1rem;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .account-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
@endpush

@section('content')
<!-- Profile Header -->
<section class="profile-header">
    <div class="container">
        <h1>Profile Settings</h1>
        <p>Manage your account information and preferences</p>
    </div>
</section>

<div class="container">
    <div class="profile-container">
        <!-- Account Stats -->
        <div class="account-stats">
            <div class="stat-item">
                <div class="stat-value">{{ $user->created_at->diffInDays() + 1 }}</div>
                <div class="stat-label">{{ $user->created_at->diffInDays() + 1 === 1 ? 'Day Active' : 'Days Active' }}</div>
            </div>

            <div class="stat-item">
                <div class="stat-value">0</div>
                <div class="stat-label">Downloads</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ $user->transactions()->count() }}</div>
                <div class="stat-label">Transactions</div>
            </div>
        </div>

        <!-- Profile Information -->
        <div class="profile-card">
            <div class="card-header">
                <h3>Profile Information</h3>
            </div>
            <div class="card-content">
                <form method="POST" action="{{ route('user.profile.update') }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="form-group">
                        <label for="name" class="form-label">Full Name</label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            class="form-input @error('name') error @enderror" 
                            value="{{ old('name', $user->name) }}" 
                            required
                        >
                        @error('name')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            class="form-input @error('email') error @enderror"
                            value="{{ old('email', $user->email) }}"
                            readonly
                            style="background-color: #f9fafb; cursor: not-allowed;"
                        >
                        <small style="color: #6b7280; font-size: 0.875rem;">Email address cannot be changed for security reasons.</small>
                        @error('email')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Account Details -->
        <div class="profile-card">
            <div class="card-header">
                <h3>Account Details</h3>
            </div>
            <div class="card-content">
                <div class="profile-info">
                    <div class="info-item">
                        <div class="info-label">Member Since</div>
                        <div class="info-value">{{ $user->created_at->format('F j, Y') }}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Last Updated</div>
                        <div class="info-value">{{ $user->updated_at->format('F j, Y') }}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Account Type</div>
                        <div class="info-value">
                            @if($user->subscriptions()->where('status', 'active')->where('end_date', '>', now())->exists())
                                Premium Member
                            @else
                                Free Member
                            @endif
                        </div>
                    </div>
                    

                </div>
            </div>
        </div>



        <!-- Danger Zone -->
        <div class="profile-card">
            <div class="card-header">
                <h3>Danger Zone</h3>
            </div>
            <div class="card-content">
                <div class="danger-zone">
                    <h4>Delete Account</h4>
                    <p>Once you delete your account, there is no going back. Please be certain.</p>
                    <button type="button" class="btn-danger" onclick="confirmDeleteAccount()">
                        <i class="fas fa-trash"></i> Delete Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function confirmDeleteAccount() {
        if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            if (confirm('This will permanently delete all your data. Are you absolutely sure?')) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ route("user.account.delete") }}';
                
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                
                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';
                
                form.appendChild(csrfToken);
                form.appendChild(methodField);
                document.body.appendChild(form);
                form.submit();
            }
        }
    }
</script>
@endpush
