@extends('layouts.user')

@section('title', 'Sign Up - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Create your account and start designing amazing social media posts')

@push('styles')
<style>
    .auth-container {
        min-height: calc(100vh - 200px);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
    }
    
    .auth-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        padding: 3rem;
        width: 100%;
        max-width: 450px;
    }
    
    .auth-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .auth-header h1 {
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
    
    .auth-header p {
        color: #64748b;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
    }
    
    .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.2s ease;
    }
    
    .form-input:focus {
        outline: none;
        border-color: var(--primary-color);
    }
    
    .form-input.error {
        border-color: #dc2626;
    }
    
    .error-message {
        color: #dc2626;
        font-size: 14px;
        margin-top: 0.5rem;
    }
    
    .form-checkbox {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .form-checkbox input {
        width: 18px;
        height: 18px;
        margin-top: 2px;
    }
    
    .form-checkbox span {
        font-size: 14px;
        line-height: 1.4;
    }
    
    .btn-full {
        width: 100%;
        padding: 14px;
        font-size: 16px;
        margin-bottom: 1rem;
    }
    
    .divider {
        text-align: center;
        margin: 2rem 0;
        position: relative;
        color: #64748b;
    }
    
    .divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: #e5e7eb;
        z-index: 1;
    }
    
    .divider span {
        background: white;
        padding: 0 1rem;
        position: relative;
        z-index: 2;
    }
    
    .google-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        width: 100%;
        padding: 14px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        background: white;
        color: #374151;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
        margin-bottom: 1rem;
    }
    
    .google-btn:hover {
        border-color: #d1d5db;
        background: #f9fafb;
        transform: translateY(-1px);
    }
    
    .google-icon {
        width: 20px;
        height: 20px;
    }
    
    .auth-footer {
        text-align: center;
        margin-top: 2rem;
        color: #64748b;
    }
    
    .auth-footer a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }
    
    .auth-footer a:hover {
        text-decoration: underline;
    }
    
    .password-strength {
        margin-top: 0.5rem;
        font-size: 12px;
    }
    
    .strength-bar {
        height: 4px;
        background: #e5e7eb;
        border-radius: 2px;
        margin-top: 0.25rem;
        overflow: hidden;
    }
    
    .strength-fill {
        height: 100%;
        transition: width 0.3s ease, background-color 0.3s ease;
        width: 0%;
        background: #dc2626;
    }
    
    .strength-fill.weak { background: #dc2626; }
    .strength-fill.fair { background: #f59e0b; }
    .strength-fill.good { background: #10b981; }
    .strength-fill.strong { background: #059669; }
    
    @media (max-width: 480px) {
        .auth-card {
            padding: 2rem 1.5rem;
            margin: 1rem;
        }
    }
</style>
@endpush

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <h1>Create Account</h1>
            <p>Join us and start creating amazing designs</p>
        </div>
        
        <!-- Google OAuth Registration -->
        <a href="{{ route('auth.google') }}" class="google-btn">
            <svg class="google-icon" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Sign up with Google
        </a>
        
        <div class="divider">
            <span>or</span>
        </div>
        
        <!-- Email/Password Registration Form -->
        <form method="POST" action="{{ route('user.register.process') }}">
            @csrf
            
            <div class="form-group">
                <label for="name" class="form-label">Full Name</label>
                <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    class="form-input @error('name') error @enderror" 
                    value="{{ old('name') }}" 
                    required 
                    autocomplete="name"
                    placeholder="Enter your full name"
                >
                @error('name')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    class="form-input @error('email') error @enderror" 
                    value="{{ old('email') }}" 
                    required 
                    autocomplete="email"
                    placeholder="Enter your email address"
                >
                @error('email')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="form-input @error('password') error @enderror" 
                    required 
                    autocomplete="new-password"
                    placeholder="Create a strong password"
                >
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <span id="strengthText">Password strength</span>
                </div>
                @error('password')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="password_confirmation" class="form-label">Confirm Password</label>
                <input 
                    type="password" 
                    id="password_confirmation" 
                    name="password_confirmation" 
                    class="form-input" 
                    required 
                    autocomplete="new-password"
                    placeholder="Confirm your password"
                >
            </div>
            
            <div class="form-group">
                <label class="form-checkbox">
                    <input type="checkbox" name="terms" required>
                    <span>I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a></span>
                </label>
                @error('terms')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <button type="submit" class="btn btn-primary btn-full">
                Create Account
            </button>
        </form>
        
        <div class="auth-footer">
            <p>Already have an account? <a href="{{ route('user.login') }}">Sign in here</a></p>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Password strength checker
    document.addEventListener('DOMContentLoaded', function() {
        const passwordInput = document.getElementById('password');
        const strengthFill = document.getElementById('strengthFill');
        const strengthText = document.getElementById('strengthText');
        
        if (passwordInput && strengthFill && strengthText) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                
                strengthFill.style.width = strength.percentage + '%';
                strengthFill.className = 'strength-fill ' + strength.class;
                strengthText.textContent = strength.text;
            });
        }
        
        function calculatePasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 8) score += 25;
            if (password.match(/[a-z]/)) score += 25;
            if (password.match(/[A-Z]/)) score += 25;
            if (password.match(/[0-9]/)) score += 25;
            if (password.match(/[^a-zA-Z0-9]/)) score += 25;
            
            if (score <= 25) {
                return { percentage: 25, class: 'weak', text: 'Weak password' };
            } else if (score <= 50) {
                return { percentage: 50, class: 'fair', text: 'Fair password' };
            } else if (score <= 75) {
                return { percentage: 75, class: 'good', text: 'Good password' };
            } else {
                return { percentage: 100, class: 'strong', text: 'Strong password' };
            }
        }
        
        // Add loading state to forms
        const forms = document.querySelectorAll('form');
        const googleBtn = document.querySelector('.google-btn');
        
        forms.forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.innerHTML = '<span class="spinner"></span> Creating account...';
                    submitBtn.disabled = true;
                }
            });
        });
        
        if (googleBtn) {
            googleBtn.addEventListener('click', function() {
                this.innerHTML = '<span class="spinner"></span> Redirecting to Google...';
            });
        }
    });
</script>
@endpush
