@extends('layouts.user')

@section('title', 'Dashboard - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', 'Manage your account, businesses, and design projects')

@push('styles')
<style>
    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .dashboard-header h1 {
        color: white;
        margin-bottom: 0.5rem;
    }
    
    .dashboard-header p {
        opacity: 0.9;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }
    
    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-left: 4px solid var(--primary-color);
    }
    
    .stat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        background: var(--primary-color);
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .stat-label {
        color: #64748b;
        font-size: 0.9rem;
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .dashboard-section {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .section-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .section-header h3 {
        margin: 0;
        color: #1e293b;
    }
    
    .section-content {
        padding: 1.5rem;
    }
    
    .business-list {
        space-y: 1rem;
    }
    
    .business-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .business-item.default {
        border-color: var(--primary-color);
        background: #f0f9ff;
    }
    
    .business-logo {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        object-fit: cover;
        background: #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
    }
    
    .business-info {
        flex: 1;
    }
    
    .business-name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .business-meta {
        font-size: 0.875rem;
        color: #64748b;
    }
    
    .default-badge {
        background: var(--primary-color);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .subscription-card {
        text-align: center;
        padding: 2rem;
    }
    
    .subscription-status {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 1rem;
    }
    
    .status-active {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-inactive {
        background: #fef2f2;
        color: #dc2626;
    }
    
    .wallet-balance {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .transaction-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .transaction-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f5f9;
    }
    
    .transaction-item:last-child {
        border-bottom: none;
    }
    
    .transaction-info {
        flex: 1;
    }
    
    .transaction-desc {
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }
    
    .transaction-date {
        font-size: 0.875rem;
        color: #64748b;
    }
    
    .transaction-amount {
        font-weight: 600;
    }
    
    .amount-credit {
        color: #059669;
    }
    
    .amount-debit {
        color: #dc2626;
    }
    
    .empty-state {
        text-align: center;
        padding: 2rem;
        color: #64748b;
    }
    
    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #cbd5e1;
    }
    
    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .business-item {
            flex-direction: column;
            text-align: center;
        }
    }
</style>
@endpush

@section('content')
<!-- Dashboard Header -->
<section class="dashboard-header">
    <div class="container">
        <h1>Welcome back, {{ $user->name }}!</h1>
        <p>Here's what's happening with your account</p>
    </div>
</section>

<!-- Stats Grid -->
<div class="container">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-download"></i>
                </div>
            </div>
            <div class="stat-value">{{ $stats['total_downloads'] }}</div>
            <div class="stat-label">Total Downloads</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-calendar-month"></i>
                </div>
            </div>
            <div class="stat-value">{{ $stats['this_month_downloads'] }}</div>
            <div class="stat-label">This Month</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-building"></i>
                </div>
            </div>
            <div class="stat-value">{{ $stats['total_businesses'] }}</div>
            <div class="stat-label">Business Profiles</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-wallet"></i>
                </div>
            </div>
            <div class="stat-value">₹{{ number_format($stats['wallet_balance'], 2) }}</div>
            <div class="stat-label">Wallet Balance</div>
        </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Business Profiles Section -->
        <div class="dashboard-section">
            <div class="section-header">
                <h3>Business Profiles</h3>
                <a href="#" class="btn btn-primary" onclick="openBusinessModal()">
                    <i class="fas fa-plus"></i> Add Business
                </a>
            </div>
            <div class="section-content">
                @if($businesses->count() > 0)
                    <div class="business-list">
                        @foreach($businesses as $business)
                            <div class="business-item {{ $business->is_default ? 'default' : '' }}">
                                <div class="business-logo">
                                    @if($business->logo_path)
                                        <img src="{{ Storage::url($business->logo_path) }}" alt="{{ $business->name }}" class="business-logo">
                                    @else
                                        <i class="fas fa-building"></i>
                                    @endif
                                </div>
                                <div class="business-info">
                                    <div class="business-name">
                                        {{ $business->name }}
                                        @if($business->is_default)
                                            <span class="default-badge">Default</span>
                                        @endif
                                    </div>
                                    <div class="business-meta">
                                        @if($business->email)
                                            {{ $business->email }}
                                        @elseif($business->phone)
                                            {{ $business->phone }}
                                        @else
                                            No contact info
                                        @endif
                                    </div>
                                </div>
                                <div class="business-actions">
                                    <button class="btn btn-outline" onclick="editBusiness({{ $business->id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state">
                        <i class="fas fa-building"></i>
                        <h4>No Business Profiles</h4>
                        <p>Add your first business profile to get started</p>
                        <button class="btn btn-primary" onclick="openBusinessModal()">
                            <i class="fas fa-plus"></i> Add Business
                        </button>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div>
            <!-- Subscription Status -->
            <div class="dashboard-section" style="margin-bottom: 1.5rem;">
                <div class="section-header">
                    <h3>Subscription</h3>
                </div>
                <div class="section-content">
                    <div class="subscription-card">
                        @if($activeSubscription)
                            <div class="subscription-status status-active">
                                <i class="fas fa-check-circle"></i>
                                Active
                            </div>
                            <h4>{{ $activeSubscription->plan->name }}</h4>
                            <p>Expires: {{ $activeSubscription->end_date->format('M d, Y') }}</p>
                        @else
                            <div class="subscription-status status-inactive">
                                <i class="fas fa-times-circle"></i>
                                No Active Plan
                            </div>
                            <p>Upgrade to access premium features</p>
                            <a href="#" class="btn btn-primary">View Plans</a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h3>Recent Transactions</h3>
                </div>
                <div class="section-content">
                    @if($recentTransactions->count() > 0)
                        <div class="transaction-list">
                            @foreach($recentTransactions as $transaction)
                                <div class="transaction-item">
                                    <div class="transaction-info">
                                        <div class="transaction-desc">{{ $transaction->description }}</div>
                                        <div class="transaction-date">{{ $transaction->created_at->format('M d, Y') }}</div>
                                    </div>
                                    <div class="transaction-amount {{ $transaction->type === 'credit' ? 'amount-credit' : 'amount-debit' }}">
                                        {{ $transaction->type === 'credit' ? '+' : '-' }}₹{{ number_format($transaction->amount, 2) }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="empty-state">
                            <i class="fas fa-receipt"></i>
                            <h4>No Transactions</h4>
                            <p>Your transaction history will appear here</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function openBusinessModal() {
        // This will be implemented when we create the business management modal
        alert('Business management modal will be implemented in the next phase');
    }
    
    function editBusiness(businessId) {
        // This will be implemented when we create the business management modal
        alert('Edit business functionality will be implemented in the next phase');
    }
</script>
@endpush
