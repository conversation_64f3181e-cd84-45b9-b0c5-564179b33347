<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- SEO Meta Tags -->
    <title>@yield('title', $branding['application_name'] ?? 'Social Media Post Creator')</title>
    <meta name="description" content="@yield('description', $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly')">
    <meta name="keywords" content="@yield('keywords', 'social media, poster, design, templates, business cards')">
    <meta name="author" content="{{ $branding['application_name'] ?? 'Social Media Post Creator' }}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', $branding['application_name'] ?? 'Social Media Post Creator')">
    <meta property="og:description" content="@yield('og_description', $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly')">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:site_name" content="{{ $branding['application_name'] ?? 'Social Media Post Creator' }}">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', $branding['application_name'] ?? 'Social Media Post Creator')">
    <meta name="twitter:description" content="@yield('twitter_description', $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly')">
    @if(!empty($branding['app_logo']))
        <meta name="twitter:image" content="{{ asset('storage/' . $branding['app_logo']) }}">
    @endif

    <!-- Additional SEO Meta Tags -->
    <meta name="robots" content="@yield('robots', 'index,follow')">
    <meta name="googlebot" content="@yield('googlebot', 'index,follow')">
    <link rel="canonical" href="{{ url()->current() }}">

    <!-- Favicon -->
    @if(!empty($branding['app_favicon']))
        <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . $branding['app_favicon']) }}">
    @else
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    @endif
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family={{ urlencode($appearance['heading_font'] ?? 'Inter') }}:wght@300;400;500;600;700&family={{ urlencode($appearance['body_font'] ?? 'Inter') }}:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "{{ $branding['application_name'] ?? 'Social Media Post Creator' }}",
        "description": "{{ $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly' }}",
        "url": "{{ url('/') }}",
        "applicationCategory": "DesignApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "INR",
            "availability": "https://schema.org/InStock"
        },
        "creator": {
            "@type": "Organization",
            "name": "{{ $branding['application_name'] ?? 'Social Media Post Creator' }}"
        }
    }
    </script>

    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: {{ $appearance['primary_color'] ?? '#3b82f6' }};
            --secondary-color: {{ $appearance['secondary_color'] ?? '#10b981' }};
            --background-color: {{ $appearance['background_color'] ?? '#f8fafc' }};
            --button-color: {{ $appearance['button_color'] ?? '#3b82f6' }};
            --heading-font: '{{ $appearance['heading_font'] ?? 'Inter' }}', sans-serif;
            --body-font: '{{ $appearance['body_font'] ?? 'Inter' }}', sans-serif;
            --heading-font-size: {{ $appearance['heading_font_size'] ?? '24' }}px;
            --body-font-size: {{ $appearance['body_font_size'] ?? '16' }}px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--body-font);
            font-size: var(--body-font-size);
            background-color: var(--background-color);
            color: #334155;
            line-height: 1.6;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--heading-font);
            font-weight: 600;
            color: #1e293b;
        }
        
        h1 { font-size: calc(var(--heading-font-size) * 1.5); }
        h2 { font-size: calc(var(--heading-font-size) * 1.25); }
        h3 { font-size: var(--heading-font-size); }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: color-mix(in srgb, var(--primary-color) 85%, black);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: color-mix(in srgb, var(--secondary-color) 85%, black);
            transform: translateY(-1px);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        
        .btn-outline:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
            color: #1e293b;
        }
        
        .logo img,
        .logo-image {
            height: 40px;
            width: auto;
            max-width: 200px;
            object-fit: contain;
        }
        
        .logo-text {
            font-family: var(--heading-font);
            font-size: 24px;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            align-items: center;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            transition: color 0.2s ease;
        }
        
        .nav-links a:hover {
            color: var(--primary-color);
        }
        
        .user-menu {
            position: relative;
        }
        
        .main-content {
            min-height: calc(100vh - 80px);
            padding: 2rem 0;
        }
        
        .footer {
            background: #1e293b;
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .footer-section h3 {
            color: white;
            margin-bottom: 1rem;
        }
        
        .footer-section p, .footer-section a {
            color: #94a3b8;
            text-decoration: none;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .footer-section a:hover {
            color: var(--primary-color);
        }
        
        .footer-bottom {
            border-top: 1px solid #334155;
            padding-top: 1rem;
            text-align: center;
            color: #94a3b8;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                flex-direction: column;
                gap: 1rem;
            }
            
            .container {
                padding: 0 15px;
            }
            
            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
        }
        
        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Alert Messages */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background-color: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .alert-error {
            background-color: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .alert-info {
            background-color: #dbeafe;
            color: #1d4ed8;
            border: 1px solid #bfdbfe;
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="{{ route('user.home') }}" class="logo">
                    @if(!empty($branding['app_logo']))
                        <img src="{{ asset('storage/' . $branding['app_logo']) }}" alt="{{ $branding['application_name'] ?? 'Logo' }}" class="logo-image">
                    @else
                        <span class="logo-text">{{ $branding['application_name'] ?? 'Social Media Post Creator' }}</span>
                    @endif
                </a>
                
                <ul class="nav-links">
                    @auth
                        <li><a href="{{ route('user.home') }}">Home</a></li>
                        <li><a href="{{ route('user.categories') }}">Categories</a></li>
                        <li><a href="{{ route('user.payment.plans') }}">Plans</a></li>
                        <li><a href="{{ route('user.downloads') }}">Downloads</a></li>
                        <li><a href="{{ route('user.dashboard') }}">Dashboard</a></li>
                        <li class="user-menu">
                            <a href="{{ route('user.profile') }}">{{ Auth::user()->name }}</a>
                        </li>
                        <li>
                            <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                                @csrf
                                <button type="submit" class="btn btn-outline">Logout</button>
                            </form>
                        </li>
                    @else
                        <li><a href="{{ route('user.login') }}" class="btn btn-primary">Login with Google</a></li>
                    @endauth
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif
            
            @if(session('error'))
                <div class="alert alert-error">
                    {{ session('error') }}
                </div>
            @endif
            
            @if(session('info'))
                <div class="alert alert-info">
                    {{ session('info') }}
                </div>
            @endif
            
            @yield('content')
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>{{ $branding['application_name'] ?? 'Social Media Post Creator' }}</h3>
                    <p>{{ $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly' }}</p>
                    <div style="margin-top: 1rem;">
                        <a href="#" style="margin-right: 1rem; color: #94a3b8; font-size: 1.25rem;">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" style="margin-right: 1rem; color: #94a3b8; font-size: 1.25rem;">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" style="margin-right: 1rem; color: #94a3b8; font-size: 1.25rem;">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" style="margin-right: 1rem; color: #94a3b8; font-size: 1.25rem;">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <a href="{{ route('user.home') }}">Home</a>
                    <a href="{{ route('user.categories') }}">Browse Templates</a>
                    <a href="{{ route('user.payment.plans') }}">Pricing Plans</a>
                    @auth
                        <a href="{{ route('user.dashboard') }}">Dashboard</a>
                        <a href="{{ route('user.downloads') }}">My Downloads</a>
                    @else
                        <a href="{{ route('user.login') }}">Sign In</a>
                    @endauth
                </div>

                <div class="footer-section">
                    <h3>Categories</h3>
                    <a href="#">Business Posts</a>
                    <a href="#">Social Media</a>
                    <a href="#">Marketing</a>
                    <a href="#">Events</a>
                    <a href="#">Announcements</a>
                </div>

                <div class="footer-section">
                    <h3>Support & Legal</h3>
                    <a href="{{ route('faq') }}">FAQ</a>
                    <a href="{{ route('contact') }}">Contact Support</a>
                    <a href="{{ route('about') }}">About Us</a>
                    <a href="{{ route('privacy-policy') }}">Privacy Policy</a>
                    <a href="{{ route('terms-of-service') }}">Terms of Service</a>
                </div>
            </div>

            <div class="footer-bottom">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                    <p>&copy; {{ date('Y') }} {{ $branding['application_name'] ?? 'Social Media Post Creator' }}. All rights reserved.</p>
                    <div style="display: flex; gap: 1rem; font-size: 0.875rem;">
                        <a href="{{ route('sitemap') }}" style="color: #94a3b8;">Sitemap</a>
                        <a href="{{ route('robots') }}" style="color: #94a3b8;">Robots</a>
                        <span style="color: #94a3b8;">Made with ❤️ in India</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // CSRF Token for AJAX requests
        window.csrfToken = '{{ csrf_token() }}';
        
        // App Configuration
        window.appConfig = {
            apiUrl: '{{ url('/api') }}',
            baseUrl: '{{ url('/') }}',
            user: @auth {!! json_encode(Auth::user()) !!} @else null @endauth
        };
    </script>
    
    @stack('scripts')
</body>
</html>
