@extends('layouts.user')

@section('title', $seoData['title'] . ' - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', $seoData['description'])
@section('keywords', $seoData['keywords'])

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
    }
    
    .page-header h1 {
        color: white;
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .page-header p {
        font-size: 1.25rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .contact-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 4rem 1rem;
    }
    
    .contact-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        margin-bottom: 4rem;
    }
    
    .contact-info {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .contact-info h2 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
    }
    
    .contact-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 8px;
    }
    
    .contact-icon {
        width: 50px;
        height: 50px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }
    
    .contact-details h3 {
        margin: 0 0 0.5rem 0;
        color: #1e293b;
    }
    
    .contact-details p {
        margin: 0;
        color: #64748b;
    }
    
    .contact-form {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .contact-form h2 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
    }
    
    .form-input,
    .form-textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.2s ease;
    }
    
    .form-input:focus,
    .form-textarea:focus {
        outline: none;
        border-color: var(--primary-color);
    }
    
    .form-textarea {
        min-height: 120px;
        resize: vertical;
    }
    
    .btn-submit {
        background: var(--primary-color);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
        width: 100%;
    }
    
    .btn-submit:hover {
        background: var(--secondary-color);
    }
    
    .faq-section {
        background: #f8fafc;
        padding: 3rem 0;
        margin-top: 4rem;
    }
    
    .faq-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 1rem;
    }
    
    .faq-section h2 {
        text-align: center;
        color: var(--primary-color);
        margin-bottom: 2rem;
    }
    
    .faq-item {
        background: white;
        margin-bottom: 1rem;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .faq-question {
        padding: 1.5rem;
        background: white;
        border: none;
        width: 100%;
        text-align: left;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .faq-answer {
        padding: 0 1.5rem 1.5rem;
        color: #64748b;
        line-height: 1.6;
    }
    
    @media (max-width: 768px) {
        .contact-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .page-header h1 {
            font-size: 2rem;
        }
        
        .contact-container {
            padding: 2rem 1rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>Contact Us</h1>
        <p>Get in touch with our support team. We're here to help you create amazing social media content!</p>
    </div>
</section>

<div class="contact-container">
    <div class="contact-grid">
        <!-- Contact Information -->
        <div class="contact-info">
            <h2>Get in Touch</h2>
            
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="contact-details">
                    <h3>Email Support</h3>
                    <p><EMAIL></p>
                    <p>We typically respond within 24 hours</p>
                </div>
            </div>
            
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="contact-details">
                    <h3>Support Hours</h3>
                    <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                    <p>Saturday: 10:00 AM - 4:00 PM</p>
                </div>
            </div>
            
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <div class="contact-details">
                    <h3>Premium Support</h3>
                    <p>Priority support for premium subscribers</p>
                    <p>Response time: Within 4 hours</p>
                </div>
            </div>
            
            <div class="contact-item">
                <div class="contact-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="contact-details">
                    <h3>FAQ</h3>
                    <p>Check our frequently asked questions</p>
                    <p><a href="{{ route('faq') }}" style="color: var(--primary-color);">Visit FAQ Page</a></p>
                </div>
            </div>
        </div>
        
        <!-- Contact Form -->
        <div class="contact-form">
            <h2>Send us a Message</h2>
            
            <form action="#" method="POST">
                @csrf
                
                <div class="form-group">
                    <label for="name" class="form-label">Full Name *</label>
                    <input type="text" id="name" name="name" class="form-input" required placeholder="Enter your full name">
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <input type="email" id="email" name="email" class="form-input" required placeholder="Enter your email address">
                </div>
                
                <div class="form-group">
                    <label for="subject" class="form-label">Subject *</label>
                    <input type="text" id="subject" name="subject" class="form-input" required placeholder="What is this regarding?">
                </div>
                
                <div class="form-group">
                    <label for="message" class="form-label">Message *</label>
                    <textarea id="message" name="message" class="form-textarea" required placeholder="Please describe your question or issue in detail..."></textarea>
                </div>
                
                <button type="submit" class="btn-submit">
                    <i class="fas fa-paper-plane"></i> Send Message
                </button>
            </form>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<section class="faq-section">
    <div class="faq-container">
        <h2>Quick Answers</h2>
        
        <div class="faq-item">
            <button class="faq-question" onclick="toggleFaq(this)">
                How do I get started with creating designs?
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="faq-answer" style="display: none;">
                Simply sign up with your Google account, browse our template categories, select a design you like, and customize it with our easy-to-use editor.
            </div>
        </div>
        
        <div class="faq-item">
            <button class="faq-question" onclick="toggleFaq(this)">
                What file formats can I download?
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="faq-answer" style="display: none;">
                You can download your designs in PNG and JPG formats. Premium users get access to high-resolution files and additional formats.
            </div>
        </div>
        
        <div class="faq-item">
            <button class="faq-question" onclick="toggleFaq(this)">
                How do I upgrade to premium?
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="faq-answer" style="display: none;">
                Visit our pricing page to see available plans and upgrade your account. Premium users get access to exclusive templates and priority support.
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
function toggleFaq(button) {
    const answer = button.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (answer.style.display === 'none') {
        answer.style.display = 'block';
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    } else {
        answer.style.display = 'none';
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    }
}
</script>
@endpush
