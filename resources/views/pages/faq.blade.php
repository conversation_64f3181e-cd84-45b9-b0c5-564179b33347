@extends('layouts.user')

@section('title', $seoData['title'] . ' - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', $seoData['description'])
@section('keywords', $seoData['keywords'])

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .page-header h1 {
        color: white;
        margin-bottom: 1rem;
    }
    
    .faq-container {
        max-width: 800px;
        margin: 0 auto;
        margin-bottom: 3rem;
    }
    
    .faq-item {
        background: white;
        border-radius: 12px;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .faq-question {
        padding: 1.5rem;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: white;
        border: none;
        width: 100%;
        text-align: left;
        font-size: 1.125rem;
        font-weight: 600;
        color: #1e293b;
        transition: background-color 0.2s ease;
    }
    
    .faq-question:hover {
        background: #f8fafc;
    }
    
    .faq-question.active {
        background: #f0f9ff;
        color: var(--primary-color);
    }
    
    .faq-icon {
        font-size: 1.25rem;
        transition: transform 0.3s ease;
    }
    
    .faq-question.active .faq-icon {
        transform: rotate(180deg);
    }
    
    .faq-answer {
        padding: 0 1.5rem;
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
        background: #f8fafc;
    }
    
    .faq-answer.active {
        padding: 1.5rem;
        max-height: 500px;
    }
    
    .faq-answer p {
        color: #4b5563;
        line-height: 1.6;
        margin: 0;
    }
    
    .search-box {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .search-input {
        width: 100%;
        max-width: 400px;
        padding: 12px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.2s ease;
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--primary-color);
    }
    
    .no-results {
        text-align: center;
        padding: 2rem;
        color: #64748b;
        display: none;
    }
    
    .contact-cta {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        margin-top: 3rem;
    }
    
    .contact-cta h3 {
        color: white;
        margin-bottom: 1rem;
    }
    
    .contact-cta p {
        margin-bottom: 1.5rem;
        opacity: 0.9;
    }
    
    @media (max-width: 768px) {
        .faq-container {
            margin: 0 1rem;
        }
        
        .faq-question {
            padding: 1rem;
            font-size: 1rem;
        }
        
        .faq-answer.active {
            padding: 1rem;
        }
        
        .search-box {
            padding: 1.5rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>Frequently Asked Questions</h1>
        <p>Find answers to common questions about our platform</p>
    </div>
</section>

<div class="container">
    <div class="faq-container">
        <!-- Search Box -->
        <div class="search-box">
            <h3 style="margin-bottom: 1rem;">Search FAQs</h3>
            <input type="text" class="search-input" id="faqSearch" placeholder="Type your question here...">
        </div>
        
        <!-- FAQ Items -->
        <div id="faqList">
            @foreach($faqs as $index => $faq)
                <div class="faq-item" data-question="{{ strtolower($faq['question']) }}" data-answer="{{ strtolower($faq['answer']) }}">
                    <button class="faq-question" onclick="toggleFaq({{ $index }})">
                        <span>{{ $faq['question'] }}</span>
                        <i class="fas fa-chevron-down faq-icon"></i>
                    </button>
                    <div class="faq-answer" id="faq-{{ $index }}">
                        <p>{{ $faq['answer'] }}</p>
                    </div>
                </div>
            @endforeach
        </div>
        
        <!-- No Results Message -->
        <div class="no-results" id="noResults">
            <i class="fas fa-search fa-3x" style="color: #cbd5e1; margin-bottom: 1rem;"></i>
            <h3>No results found</h3>
            <p>Try searching with different keywords or browse all questions above.</p>
        </div>
        
        <!-- Contact CTA -->
        <div class="contact-cta">
            <h3>Still have questions?</h3>
            <p>Can't find what you're looking for? Our support team is here to help!</p>
            <a href="#" class="btn btn-outline" style="background: white; color: var(--primary-color); border-color: white;">
                <i class="fas fa-envelope"></i> Contact Support
            </a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function toggleFaq(index) {
        const question = document.querySelector(`[onclick="toggleFaq(${index})"]`);
        const answer = document.getElementById(`faq-${index}`);
        
        // Close all other FAQs
        document.querySelectorAll('.faq-question').forEach(q => {
            if (q !== question) {
                q.classList.remove('active');
            }
        });
        
        document.querySelectorAll('.faq-answer').forEach(a => {
            if (a !== answer) {
                a.classList.remove('active');
            }
        });
        
        // Toggle current FAQ
        question.classList.toggle('active');
        answer.classList.toggle('active');
    }
    
    // Search functionality
    document.getElementById('faqSearch').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const faqItems = document.querySelectorAll('.faq-item');
        const noResults = document.getElementById('noResults');
        let hasResults = false;
        
        faqItems.forEach(item => {
            const question = item.dataset.question;
            const answer = item.dataset.answer;
            
            if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                item.style.display = 'block';
                hasResults = true;
            } else {
                item.style.display = 'none';
            }
        });
        
        noResults.style.display = hasResults ? 'none' : 'block';
    });
    
    // Auto-expand first FAQ on page load
    document.addEventListener('DOMContentLoaded', function() {
        if (document.querySelectorAll('.faq-item').length > 0) {
            toggleFaq(0);
        }
    });
</script>
@endpush
