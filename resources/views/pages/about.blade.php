@extends('layouts.user')

@section('title', $seoData['title'] . ' - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', $seoData['description'])
@section('keywords', $seoData['keywords'])

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
    }
    
    .page-header h1 {
        color: white;
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .page-header p {
        font-size: 1.25rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .about-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 4rem 1rem;
    }
    
    .about-section {
        margin-bottom: 4rem;
    }
    
    .about-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        align-items: center;
        margin-bottom: 4rem;
    }
    
    .about-text h2 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        font-size: 2.5rem;
    }
    
    .about-text p {
        color: #64748b;
        line-height: 1.8;
        margin-bottom: 1.5rem;
        font-size: 1.1rem;
    }
    
    .about-image {
        text-align: center;
    }
    
    .about-image img {
        max-width: 100%;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin: 4rem 0;
    }
    
    .stat-card {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #64748b;
        font-weight: 500;
    }
    
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin: 4rem 0;
    }
    
    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .feature-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin: 0 auto 1.5rem;
    }
    
    .feature-card h3 {
        color: #1e293b;
        margin-bottom: 1rem;
    }
    
    .feature-card p {
        color: #64748b;
        line-height: 1.6;
    }
    
    .mission-section {
        background: #f8fafc;
        padding: 4rem 0;
        margin: 4rem 0;
        border-radius: 12px;
    }
    
    .mission-content {
        max-width: 800px;
        margin: 0 auto;
        text-align: center;
        padding: 0 2rem;
    }
    
    .mission-content h2 {
        color: var(--primary-color);
        margin-bottom: 2rem;
        font-size: 2.5rem;
    }
    
    .mission-content p {
        color: #64748b;
        line-height: 1.8;
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
    }
    
    .cta-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 2rem;
        border-radius: 12px;
        text-align: center;
    }
    
    .cta-section h2 {
        color: white;
        margin-bottom: 1rem;
        font-size: 2.5rem;
    }
    
    .cta-section p {
        opacity: 0.9;
        margin-bottom: 2rem;
        font-size: 1.2rem;
    }
    
    .btn-cta {
        background: white;
        color: var(--primary-color);
        padding: 15px 30px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: transform 0.2s ease;
    }
    
    .btn-cta:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }
    
    @media (max-width: 768px) {
        .about-content {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .page-header h1 {
            font-size: 2rem;
        }
        
        .about-text h2 {
            font-size: 2rem;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .features-grid {
            grid-template-columns: 1fr;
        }
        
        .about-container {
            padding: 2rem 1rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>About Us</h1>
        <p>Empowering businesses to create stunning social media content with ease and creativity</p>
    </div>
</section>

<div class="about-container">
    <!-- Main About Section -->
    <div class="about-content">
        <div class="about-text">
            <h2>Our Story</h2>
            <p>
                We started with a simple mission: to make professional-quality social media design accessible to everyone. 
                Whether you're a small business owner, entrepreneur, or marketing professional, creating eye-catching 
                social media content shouldn't require years of design experience or expensive software.
            </p>
            <p>
                Our platform combines powerful design tools with an intuitive interface, allowing you to create 
                stunning posts, stories, and marketing materials in minutes, not hours.
            </p>
        </div>
        <div class="about-image">
            <div style="background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); 
                        width: 400px; height: 300px; border-radius: 12px; margin: 0 auto;
                        display: flex; align-items: center; justify-content: center; color: white; font-size: 4rem;">
                <i class="fas fa-palette"></i>
            </div>
        </div>
    </div>
    
    <!-- Stats Section -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">10K+</div>
            <div class="stat-label">Happy Users</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">500+</div>
            <div class="stat-label">Design Templates</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">50K+</div>
            <div class="stat-label">Designs Created</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">99%</div>
            <div class="stat-label">Customer Satisfaction</div>
        </div>
    </div>
    
    <!-- Features Section -->
    <div class="features-grid">
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-magic"></i>
            </div>
            <h3>Easy to Use</h3>
            <p>Our drag-and-drop editor makes design simple for everyone, regardless of experience level.</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-bolt"></i>
            </div>
            <h3>Lightning Fast</h3>
            <p>Create professional designs in minutes with our optimized tools and pre-made templates.</p>
        </div>
        
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3>Mobile Friendly</h3>
            <p>Design on any device with our responsive platform that works seamlessly across all screens.</p>
        </div>
    </div>
</div>

<!-- Mission Section -->
<section class="mission-section">
    <div class="mission-content">
        <h2>Our Mission</h2>
        <p>
            To democratize design and empower every business to tell their story through beautiful, 
            professional social media content. We believe that great design should be accessible to all, 
            not just those with extensive training or expensive tools.
        </p>
        <p>
            We're committed to continuously improving our platform, adding new features, and expanding 
            our template library to meet the evolving needs of modern businesses and creators.
        </p>
    </div>
</section>

<div class="about-container">
    <!-- Call to Action -->
    <div class="cta-section">
        <h2>Ready to Get Started?</h2>
        <p>Join thousands of businesses already creating amazing content with our platform</p>
        @auth
            <a href="{{ route('user.categories') }}" class="btn-cta">
                <i class="fas fa-rocket"></i> Browse Templates
            </a>
        @else
            <a href="{{ route('user.login') }}" class="btn-cta">
                <i class="fas fa-sign-in-alt"></i> Sign Up with Google
            </a>
        @endauth
    </div>
</div>
@endsection
