@extends('layouts.user')

@section('title', $seoData['title'] . ' - ' . ($branding['application_name'] ?? 'Social Media Post Creator'))
@section('description', $seoData['description'])
@section('keywords', $seoData['keywords'])

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
    }
    
    .page-header h1 {
        color: white;
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .page-header p {
        font-size: 1.25rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .terms-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 4rem 1rem;
    }
    
    .terms-content {
        background: white;
        padding: 3rem;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        line-height: 1.8;
    }
    
    .terms-content h2 {
        color: var(--primary-color);
        margin: 2rem 0 1rem 0;
        font-size: 1.5rem;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.5rem;
    }
    
    .terms-content h3 {
        color: #1e293b;
        margin: 1.5rem 0 1rem 0;
        font-size: 1.25rem;
    }
    
    .terms-content p {
        color: #64748b;
        margin-bottom: 1rem;
    }
    
    .terms-content ul {
        color: #64748b;
        margin: 1rem 0 1rem 2rem;
    }
    
    .terms-content li {
        margin-bottom: 0.5rem;
    }
    
    .last-updated {
        background: #f8fafc;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: center;
        color: #64748b;
        font-style: italic;
    }
    
    .contact-info {
        background: #f8fafc;
        padding: 2rem;
        border-radius: 8px;
        margin-top: 2rem;
        text-align: center;
    }
    
    .contact-info h3 {
        color: var(--primary-color);
        margin-bottom: 1rem;
    }
    
    .contact-info p {
        color: #64748b;
        margin-bottom: 0.5rem;
    }
    
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .terms-container {
            padding: 2rem 1rem;
        }
        
        .terms-content {
            padding: 2rem 1.5rem;
        }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>Terms of Service</h1>
        <p>Please read these terms carefully before using our social media design platform</p>
    </div>
</section>

<div class="terms-container">
    <div class="terms-content">
        <div class="last-updated">
            Last updated: {{ date('F j, Y') }}
        </div>
        
        <h2>1. Acceptance of Terms</h2>
        <p>
            By accessing and using {{ $branding['application_name'] ?? 'Social Media Post Creator' }} ("the Service"), 
            you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to 
            abide by the above, please do not use this service.
        </p>
        
        <h2>2. Description of Service</h2>
        <p>
            {{ $branding['application_name'] ?? 'Social Media Post Creator' }} is a web-based platform that provides 
            tools and templates for creating social media content, including but not limited to posts, stories, 
            and marketing materials.
        </p>
        
        <h2>3. User Accounts</h2>
        <h3>3.1 Account Creation</h3>
        <p>
            To access certain features of the Service, you must create an account using Google OAuth authentication. 
            You are responsible for maintaining the confidentiality of your account information.
        </p>
        
        <h3>3.2 Account Responsibilities</h3>
        <ul>
            <li>You must provide accurate and complete information when creating your account</li>
            <li>You are responsible for all activities that occur under your account</li>
            <li>You must notify us immediately of any unauthorized use of your account</li>
            <li>You may not share your account credentials with others</li>
        </ul>
        
        <h2>4. Acceptable Use</h2>
        <h3>4.1 Permitted Uses</h3>
        <p>You may use the Service to:</p>
        <ul>
            <li>Create original designs for personal or commercial use</li>
            <li>Download and use designs you create through the platform</li>
            <li>Share your created content on social media platforms</li>
        </ul>
        
        <h3>4.2 Prohibited Uses</h3>
        <p>You may not use the Service to:</p>
        <ul>
            <li>Create content that is illegal, harmful, or violates any laws</li>
            <li>Infringe on intellectual property rights of others</li>
            <li>Upload malicious code or attempt to compromise the platform</li>
            <li>Resell or redistribute our templates without permission</li>
            <li>Create content that is defamatory, obscene, or offensive</li>
        </ul>
        
        <h2>5. Intellectual Property</h2>
        <h3>5.1 Our Content</h3>
        <p>
            All templates, graphics, and design elements provided by the Service are owned by us or our licensors. 
            You receive a license to use these elements in your designs but do not own the underlying assets.
        </p>
        
        <h3>5.2 Your Content</h3>
        <p>
            You retain ownership of any original content you upload or create using the Service. However, 
            you grant us a license to store and process your content to provide the Service.
        </p>
        
        <h2>6. Subscription and Payments</h2>
        <h3>6.1 Subscription Plans</h3>
        <p>
            We offer both free and premium subscription plans. Premium features require a paid subscription 
            and are billed according to the plan you select.
        </p>
        
        <h3>6.2 Payment Terms</h3>
        <ul>
            <li>Subscriptions are billed in advance on a recurring basis</li>
            <li>All fees are non-refundable except as required by law</li>
            <li>We may change subscription prices with 30 days notice</li>
            <li>You may cancel your subscription at any time</li>
        </ul>
        
        <h2>7. Privacy and Data Protection</h2>
        <p>
            Your privacy is important to us. Please review our Privacy Policy, which also governs your use 
            of the Service, to understand our practices regarding your personal information.
        </p>
        
        <h2>8. Limitation of Liability</h2>
        <p>
            The Service is provided "as is" without warranties of any kind. We shall not be liable for any 
            indirect, incidental, special, consequential, or punitive damages resulting from your use of the Service.
        </p>
        
        <h2>9. Termination</h2>
        <p>
            We may terminate or suspend your account and access to the Service at our sole discretion, 
            without notice, for conduct that we believe violates these Terms or is harmful to other users, 
            us, or third parties.
        </p>
        
        <h2>10. Changes to Terms</h2>
        <p>
            We reserve the right to modify these terms at any time. We will notify users of significant 
            changes via email or through the Service. Continued use of the Service after changes constitutes 
            acceptance of the new terms.
        </p>
        
        <h2>11. Governing Law</h2>
        <p>
            These Terms shall be governed by and construed in accordance with the laws of the jurisdiction 
            in which our company is incorporated, without regard to conflict of law principles.
        </p>
        
        <div class="contact-info">
            <h3>Questions About These Terms?</h3>
            <p>If you have any questions about these Terms of Service, please contact us:</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Support:</strong> <a href="{{ route('contact') }}" style="color: var(--primary-color);">Contact Us</a></p>
        </div>
    </div>
</div>
@endsection
