<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>S.No</th>
                <th>Category</th>
                <th>Slug</th>
                <th>Parent</th>
                <th>Sort Order</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($categories as $index => $category)
                <tr class="{{ $category->parent_id ? 'subcategory' : '' }}">
                    <td><span class="serial-number">{{ $categories->firstItem() + $index }}</span></td>
                    <td>
                        <div class="category-hierarchy">
                            @if($category->icon)
                                <span class="category-icon">{{ $category->icon }}</span>
                            @endif
                            <div>
                                <strong>{{ $category->name }}</strong>
                                @if($category->description)
                                    <div class="category-path">{{ Str::limit($category->description, 50) }}</div>
                                @endif
                            </div>
                        </div>
                    </td>
                    <td><code>{{ $category->slug }}</code></td>
                    <td>
                        @if($category->parent)
                            <span class="category-path">{{ $category->parent->name }}</span>
                        @else
                            <span style="color: #64748b;">Root Category</span>
                        @endif
                    </td>
                    <td>{{ $category->sort_order }}</td>
                    <td>
                        <label class="toggle-switch">
                            <input type="checkbox" {{ $category->status ? 'checked' : '' }} 
                                   onchange="toggleStatus({{ $category->id }}, this)">
                            <span class="slider"></span>
                        </label>
                    </td>
                    <td>
                        <a href="{{ route('admin.categories.edit', $category->id) }}" class="btn btn-edit">Edit</a>
                        <form action="{{ route('admin.categories.destroy', $category->id) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-delete" onclick="return confirm('Are you sure you want to delete this category?');">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7">
                        <div class="empty-state">
                            <h3>No categories found</h3>
                            <p>No categories match your current search criteria.</p>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<div class="pagination">
    {{ $categories->appends(request()->query())->links() }}
</div>
