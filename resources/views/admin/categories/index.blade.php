@extends('layouts.admin')

@section('title', 'Categories Management')
@section('page-title', 'Categories Management')

@push('styles')
@include('admin.shared-styles')
<style>
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #10b981;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .serial-number {
        font-weight: 600;
        color: #667eea;
    }

    .category-hierarchy {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .category-icon {
        font-size: 1.2rem;
    }

    .subcategory {
        padding-left: 2rem;
        position: relative;
    }

    /* .subcategory::before {
        content: "└─";
        position: absolute;
        left: 0.5rem;
        color: #64748b;
    } */

    .category-path {
        color: #64748b;
        font-size: 0.875rem;
    }

    .filters-container {
        border: 1px solid #e5e7eb;
    }

    .filters-grid .form-group label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .filter-indicator {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .header-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    #filter-count {
        background: #ef4444;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
    }
</style>
@endpush

@section('content')
<div class="page-header">
    <h1>📂 Manage Categories</h1>
    <div class="header-actions">
        <span class="filter-indicator">
            Filters Applied: <span id="filter-count" style="display: none;">0</span>
        </span>
        <a href="{{ route('admin.categories.create') }}" class="btn btn-create">+ Add New Category</a>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

@if(session('error') || $errors->any())
    <div class="alert alert-error">
        @if(session('error'))
            {{ session('error') }}
        @endif
        @if($errors->any())
            <ul>
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        @endif
    </div>
@endif

<!-- Search and Filters -->
<div class="filters-container" style="background: #f8fafc; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; border: 1px solid #e5e7eb;">
    <form id="filter-form" class="filters-form">
        <div class="filters-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
            <div class="form-group">
                <label for="search">🔍 Search Categories</label>
                <input type="text" name="search" id="search" value="{{ request('search') }}" placeholder="Search by name, description, or slug...">
            </div>

            <div class="form-group">
                <label for="status">Status</label>
                <select name="status" id="status">
                    <option value="">-- All Status --</option>
                    <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Active</option>
                    <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <div class="form-group">
                <label for="parent_filter">Category Type</label>
                <select name="parent_filter" id="parent_filter">
                    <option value="">-- All Types --</option>
                    <option value="parent" {{ request('parent_filter') === 'parent' ? 'selected' : '' }}>Parent Categories</option>
                    <option value="subcategory" {{ request('parent_filter') === 'subcategory' ? 'selected' : '' }}>Subcategories</option>
                </select>
            </div>

            <div class="form-group" style="display: flex; align-items: end;">
                <button type="button" id="clear-filters" class="btn btn-back" style="width: 100%;">Clear Filters</button>
            </div>
        </div>
    </form>
</div>

<div id="results-container">
    @include('admin.categories.partials.results', ['categories' => $categories])
</div>



@include('admin.shared-category-script')

@push('scripts')
<script>
function toggleStatus(categoryId, checkbox) {
    const isChecked = checkbox.checked;

    fetch(`/admin/categories/${categoryId}/toggle-status`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            status: isChecked
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(data.message);
        } else {
            checkbox.checked = !isChecked;
            alert('Failed to update status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        checkbox.checked = !isChecked;
        alert('An error occurred while updating status');
    });
}

// Override the initializeToggleSwitches function for this page
function initializeToggleSwitches() {
    // Toggle switches are already handled by the toggleStatus function
    // This function is called after AJAX updates to reinitialize any new elements
}
</script>
@endpush
@endsection
