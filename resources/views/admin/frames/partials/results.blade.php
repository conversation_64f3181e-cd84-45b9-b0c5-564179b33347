<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>S.No</th>
                <th>Image</th>
                <th>Name</th>
                <th>Premium</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($frames as $index => $frame)
                <tr>
                    <td><span class="serial-number">{{ $frames->firstItem() + $index }}</span></td>
                    <td>
                        @if($frame->image_path)
                            <img src="{{ Storage::url($frame->image_path) }}" alt="{{ $frame->name }}" class="thumbnail">
                        @else
                            <span style="color: #64748b;">No Image</span>
                        @endif
                    </td>
                    <td><strong>{{ $frame->name }}</strong></td>
                    <td>
                        @if($frame->is_premium)
                            <span class="status-premium">Premium</span>
                        @else
                            <span class="status-regular">Regular</span>
                        @endif
                    </td>
                    <td>
                        <label class="toggle-switch">
                            <input type="checkbox" {{ $frame->status ? 'checked' : '' }} 
                                   onchange="toggleStatus({{ $frame->id }}, this)">
                            <span class="slider"></span>
                        </label>
                    </td>
                    <td>
                        <a href="{{ route('admin.frames.edit', $frame->id) }}" class="btn btn-edit">Edit</a>
                        <form action="{{ route('admin.frames.destroy', $frame->id) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-delete" onclick="return confirm('Are you sure you want to delete this frame?');">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7">
                        <div class="empty-state">
                            <h3>No frames found</h3>
                            <p>No frames match your current filters.</p>
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<div class="pagination">
    {{ $frames->appends(request()->query())->links() }}
</div>
