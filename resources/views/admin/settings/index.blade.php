@extends('layouts.admin')

@section('title', 'Settings')
@section('page-title', 'Settings')

@push('styles')
@include('admin.shared-styles')
<style>
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }
    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }
    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    input:checked + .slider {
        background-color: #10b981;
    }
    input:checked + .slider:before {
        transform: translateX(26px);
    }

    /* Tab Styles */
    .tabs-container {
        margin-bottom: 2rem;
    }
    .tabs-nav {
        display: flex;
        border-bottom: 2px solid #e5e7eb;
        margin-bottom: 2rem;
    }
    .tab-button {
        padding: 1rem 1.5rem;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 500;
        color: #6b7280;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;
    }
    .tab-button:hover {
        color: #374151;
        background-color: #f9fafb;
    }
    .tab-button.active {
        color: #3b82f6;
        border-bottom-color: #3b82f6;
        background-color: #eff6ff;
    }
    .tab-content {
        display: none;
    }
    .tab-content.active {
        display: block;
    }
    .settings-section {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e5e7eb;
    }
</style>
@endpush

@section('content')
<div class="page-header">
    <h1>⚙️ Application Settings</h1>
</div>

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<div class="form-container">
    <form action="{{ route('admin.settings.store') }}" method="POST" enctype="multipart/form-data">
        @csrf

        <!-- Tabs Navigation -->
        <div class="tabs-container">
            <div class="tabs-nav">
                <button type="button" class="tab-button active" data-tab="general">🏠 General</button>
                <button type="button" class="tab-button" data-tab="branding">🎨 Branding</button>
                <button type="button" class="tab-button" data-tab="appearance">🎨 Appearance</button>
                <button type="button" class="tab-button" data-tab="oauth">🔐 OAuth</button>
                <button type="button" class="tab-button" data-tab="payment">💳 Payment</button>
            </div>

            <!-- General Settings Tab -->
            <div class="tab-content active" id="general">
                <div class="settings-section">
                    <h3 class="section-title">📱 Application Settings</h3>

                    <div class="form-group">
                        <label for="subscription_enabled">Subscription Enabled</label>
                        <select name="subscription_enabled" id="subscription_enabled">
                            <option value="true" {{ ($settings['subscription_enabled']->setting_value ?? 'false') == 'true' ? 'selected' : '' }}>Enabled</option>
                            <option value="false" {{ ($settings['subscription_enabled']->setting_value ?? 'false') == 'false' ? 'selected' : '' }}>Disabled</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="pay_per_download_enabled">Pay Per Download Enabled</label>
                        <select name="pay_per_download_enabled" id="pay_per_download_enabled">
                            <option value="true" {{ ($settings['pay_per_download_enabled']->setting_value ?? 'false') == 'true' ? 'selected' : '' }}>Enabled</option>
                            <option value="false" {{ ($settings['pay_per_download_enabled']->setting_value ?? 'false') == 'false' ? 'selected' : '' }}>Disabled</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="per_download_cost">Per Download Cost (INR)</label>
                        <input type="number" name="per_download_cost" id="per_download_cost" value="{{ $settings['per_download_cost']->setting_value ?? '1.00' }}" step="0.01">
                    </div>

                    <div class="form-group">
                        <label for="free_download_enabled">Free Download Enabled</label>
                        <select name="free_download_enabled" id="free_download_enabled">
                            <option value="true" {{ ($settings['free_download_enabled']->setting_value ?? 'true') == 'true' ? 'selected' : '' }}>Enabled</option>
                            <option value="false" {{ ($settings['free_download_enabled']->setting_value ?? 'true') == 'false' ? 'selected' : '' }}>Disabled</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Branding Settings Tab -->
            <div class="tab-content" id="branding">
                <div class="settings-section">
                    <h3 class="section-title">🎨 Branding & Logo Settings</h3>

                    <!-- Application Name and Tagline -->
                    <div class="form-group">
                        <label for="application_name">Application Name</label>
                        <input type="text" name="application_name" id="application_name" value="{{ $settings['application_name']->setting_value ?? 'Social Media Post Creator' }}" placeholder="Your Application Name">
                        <small style="color: #6b7280; font-size: 0.875rem;">The main name of your application</small>
                    </div>

                    <div class="form-group">
                        <label for="application_tagline">Application Tagline</label>
                        <input type="text" name="application_tagline" id="application_tagline" value="{{ $settings['application_tagline']->setting_value ?? 'Create stunning social media posts effortlessly' }}" placeholder="Your Application Tagline">
                        <small style="color: #6b7280; font-size: 0.875rem;">A short descriptive tagline for your application</small>
                    </div>

                    <!-- Logo Upload Section -->
                    <div class="form-group">
                        <label for="app_logo_file">Application Logo</label>
                        <div class="logo-upload-container">
                            @if(isset($settings['app_logo']) && $settings['app_logo']->setting_value)
                                <div class="current-logo">
                                    <img src="{{ asset('storage/' . $settings['app_logo']->setting_value) }}" alt="Current Application Logo" style="max-width: 200px; max-height: 60px; margin-bottom: 10px; border: 1px solid #e5e7eb; border-radius: 4px; padding: 5px;">
                                    <p style="font-size: 0.875rem; color: #6b7280;">Current Logo</p>
                                </div>
                            @endif
                            <input type="file" name="app_logo_file" id="app_logo_file" accept="image/*" onchange="previewImage(this, 'app_logo_preview')">
                            <div id="app_logo_preview" class="image-preview"></div>
                        </div>
                        <small style="color: #6b7280; font-size: 0.875rem;">Upload your application logo (recommended size: 200x60px, formats: jpg, png, gif, svg)</small>
                    </div>

                    <div class="form-group">
                        <label for="app_favicon_file">Favicon</label>
                        <div class="logo-upload-container">
                            @if(isset($settings['app_favicon']) && $settings['app_favicon']->setting_value)
                                <div class="current-logo">
                                    <img src="{{ asset('storage/' . $settings['app_favicon']->setting_value) }}" alt="Current Favicon" style="max-width: 32px; max-height: 32px; margin-bottom: 10px; border: 1px solid #e5e7eb; border-radius: 4px; padding: 2px;">
                                    <p style="font-size: 0.875rem; color: #6b7280;">Current Favicon</p>
                                </div>
                            @endif
                            <input type="file" name="app_favicon_file" id="app_favicon_file" accept="image/*" onchange="previewImage(this, 'app_favicon_preview')">
                            <div id="app_favicon_preview" class="image-preview"></div>
                        </div>
                        <small style="color: #6b7280; font-size: 0.875rem;">Upload your favicon (recommended: 32x32px, formats: jpg, png, gif, svg, ico)</small>
                    </div>

                    <div class="form-group">
                        <label for="application_logo_file">Admin Panel Logo</label>
                        <div class="logo-upload-container">
                            @if(isset($settings['application_logo']) && $settings['application_logo']->setting_value)
                                <div class="current-logo">
                                    <img src="{{ asset('storage/' . $settings['application_logo']->setting_value) }}" alt="Current Admin Logo" style="max-width: 150px; max-height: 50px; margin-bottom: 10px; border: 1px solid #e5e7eb; border-radius: 4px; padding: 5px;">
                                    <p style="font-size: 0.875rem; color: #6b7280;">Current Admin Logo</p>
                                </div>
                            @endif
                            <input type="file" name="application_logo_file" id="application_logo_file" accept="image/*" onchange="previewImage(this, 'application_logo_preview')">
                            <div id="application_logo_preview" class="image-preview"></div>
                        </div>
                        <small style="color: #6b7280; font-size: 0.875rem;">Upload your admin panel logo (recommended size: 150x50px, formats: jpg, png, gif, svg)</small>
                    </div>
                </div>
            </div>

            <!-- Appearance Settings Tab -->
            <div class="tab-content" id="appearance">
                <div class="settings-section">
                    <h3 class="section-title">🎨 Appearance Customization</h3>

                    <!-- Color Settings -->
                    <div style="margin-bottom: 2rem;">
                        <h4 style="font-size: 1.1rem; font-weight: 600; color: #374151; margin-bottom: 1rem; border-bottom: 1px solid #e5e7eb; padding-bottom: 0.5rem;">🎨 Colors</h4>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <div class="form-group">
                                <label for="primary_color">Primary Color</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" name="primary_color" id="primary_color" value="{{ $settings['primary_color']->setting_value ?? '#3b82f6' }}" style="width: 50px; height: 40px; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer;">
                                    <input type="text" id="primary_color_text" value="{{ $settings['primary_color']->setting_value ?? '#3b82f6' }}" style="flex: 1; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;" readonly>
                                </div>
                                <small style="color: #6b7280; font-size: 0.875rem;">Main brand color used for buttons and highlights</small>
                            </div>

                            <div class="form-group">
                                <label for="secondary_color">Secondary Color</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" name="secondary_color" id="secondary_color" value="{{ $settings['secondary_color']->setting_value ?? '#10b981' }}" style="width: 50px; height: 40px; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer;">
                                    <input type="text" id="secondary_color_text" value="{{ $settings['secondary_color']->setting_value ?? '#10b981' }}" style="flex: 1; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;" readonly>
                                </div>
                                <small style="color: #6b7280; font-size: 0.875rem;">Secondary accent color for success states and highlights</small>
                            </div>

                            <div class="form-group">
                                <label for="background_color">Background Color</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" name="background_color" id="background_color" value="{{ $settings['background_color']->setting_value ?? '#f8fafc' }}" style="width: 50px; height: 40px; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer;">
                                    <input type="text" id="background_color_text" value="{{ $settings['background_color']->setting_value ?? '#f8fafc' }}" style="flex: 1; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;" readonly>
                                </div>
                                <small style="color: #6b7280; font-size: 0.875rem;">Main background color for the application</small>
                            </div>

                            <div class="form-group">
                                <label for="button_color">Button Color</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="color" name="button_color" id="button_color" value="{{ $settings['button_color']->setting_value ?? '#3b82f6' }}" style="width: 50px; height: 40px; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer;">
                                    <input type="text" id="button_color_text" value="{{ $settings['button_color']->setting_value ?? '#3b82f6' }}" style="flex: 1; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;" readonly>
                                </div>
                                <small style="color: #6b7280; font-size: 0.875rem;">Default color for buttons and interactive elements</small>
                            </div>
                        </div>
                    </div>

                    <!-- Typography Settings -->
                    <div style="margin-bottom: 2rem;">
                        <h4 style="font-size: 1.1rem; font-weight: 600; color: #374151; margin-bottom: 1rem; border-bottom: 1px solid #e5e7eb; padding-bottom: 0.5rem;">📝 Typography</h4>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <div class="form-group">
                                <label for="heading_font">Heading Font</label>
                                <select name="heading_font" id="heading_font">
                                    <option value="Arial" {{ ($settings['heading_font']->setting_value ?? 'Arial') == 'Arial' ? 'selected' : '' }}>Arial</option>
                                    <option value="Helvetica" {{ ($settings['heading_font']->setting_value ?? 'Arial') == 'Helvetica' ? 'selected' : '' }}>Helvetica</option>
                                    <option value="Georgia" {{ ($settings['heading_font']->setting_value ?? 'Arial') == 'Georgia' ? 'selected' : '' }}>Georgia</option>
                                    <option value="Times New Roman" {{ ($settings['heading_font']->setting_value ?? 'Arial') == 'Times New Roman' ? 'selected' : '' }}>Times New Roman</option>
                                    <option value="Verdana" {{ ($settings['heading_font']->setting_value ?? 'Arial') == 'Verdana' ? 'selected' : '' }}>Verdana</option>
                                    <option value="Trebuchet MS" {{ ($settings['heading_font']->setting_value ?? 'Arial') == 'Trebuchet MS' ? 'selected' : '' }}>Trebuchet MS</option>
                                    <option value="Courier New" {{ ($settings['heading_font']->setting_value ?? 'Arial') == 'Courier New' ? 'selected' : '' }}>Courier New</option>
                                </select>
                                <small style="color: #6b7280; font-size: 0.875rem;">Font family for headings and titles</small>
                            </div>

                            <div class="form-group">
                                <label for="body_font">Body Font</label>
                                <select name="body_font" id="body_font">
                                    <option value="Arial" {{ ($settings['body_font']->setting_value ?? 'Arial') == 'Arial' ? 'selected' : '' }}>Arial</option>
                                    <option value="Helvetica" {{ ($settings['body_font']->setting_value ?? 'Arial') == 'Helvetica' ? 'selected' : '' }}>Helvetica</option>
                                    <option value="Georgia" {{ ($settings['body_font']->setting_value ?? 'Arial') == 'Georgia' ? 'selected' : '' }}>Georgia</option>
                                    <option value="Times New Roman" {{ ($settings['body_font']->setting_value ?? 'Arial') == 'Times New Roman' ? 'selected' : '' }}>Times New Roman</option>
                                    <option value="Verdana" {{ ($settings['body_font']->setting_value ?? 'Arial') == 'Verdana' ? 'selected' : '' }}>Verdana</option>
                                    <option value="Trebuchet MS" {{ ($settings['body_font']->setting_value ?? 'Arial') == 'Trebuchet MS' ? 'selected' : '' }}>Trebuchet MS</option>
                                    <option value="Courier New" {{ ($settings['body_font']->setting_value ?? 'Arial') == 'Courier New' ? 'selected' : '' }}>Courier New</option>
                                </select>
                                <small style="color: #6b7280; font-size: 0.875rem;">Font family for body text and content</small>
                            </div>

                            <div class="form-group">
                                <label for="heading_font_size">Heading Font Size (px)</label>
                                <input type="number" name="heading_font_size" id="heading_font_size" value="{{ $settings['heading_font_size']->setting_value ?? '24' }}" min="10" max="72" placeholder="24">
                                <small style="color: #6b7280; font-size: 0.875rem;">Font size for headings (10-72px)</small>
                            </div>

                            <div class="form-group">
                                <label for="body_font_size">Body Font Size (px)</label>
                                <input type="number" name="body_font_size" id="body_font_size" value="{{ $settings['body_font_size']->setting_value ?? '16' }}" min="8" max="32" placeholder="16">
                                <small style="color: #6b7280; font-size: 0.875rem;">Font size for body text (8-32px)</small>
                            </div>
                        </div>
                    </div>

                    <!-- Reset to Defaults -->
                    <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;">
                        <button type="button" onclick="resetAppearanceDefaults()" style="background-color: #6b7280; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;">
                            🔄 Reset to Defaults
                        </button>
                        <small style="color: #6b7280; font-size: 0.875rem; margin-left: 10px;">This will reset all appearance settings to their default values</small>
                    </div>
                </div>
            </div>

            <!-- OAuth Settings Tab -->
            <div class="tab-content" id="oauth">
                <div class="settings-section">
                    <h3 class="section-title">🔐 Google OAuth Settings</h3>

                    <div class="form-group">
                        <label for="google_client_id">Google Client ID</label>
                        <input type="text" name="google_client_id" id="google_client_id" value="{{ $settings['google_client_id']->setting_value ?? '' }}" placeholder="Enter Google OAuth Client ID">
                        <small style="color: #6b7280; font-size: 0.875rem;">Get this from Google Cloud Console → APIs & Services → Credentials</small>
                    </div>

                    <div class="form-group">
                        <label for="google_client_secret">Google Client Secret</label>
                        <input type="password" name="google_client_secret" id="google_client_secret" value="{{ $settings['google_client_secret']->setting_value ?? '' }}" placeholder="Enter Google OAuth Client Secret">
                        <small style="color: #6b7280; font-size: 0.875rem;">Keep this secret and secure</small>
                    </div>

                    <div class="form-group">
                        <label for="google_redirect_url">Google Redirect URL</label>
                        <input type="url" name="google_redirect_url" id="google_redirect_url" value="{{ $settings['google_redirect_url']->setting_value ?? '' }}" placeholder="http://127.0.0.1:8000/auth/callback/google">
                        <small style="color: #6b7280; font-size: 0.875rem;">Must match the redirect URI in Google Cloud Console</small>
                    </div>
                </div>
            </div>

            <!-- Payment Settings Tab -->
            <div class="tab-content" id="payment">
                <div class="settings-section">
                    <h3 class="section-title">💳 Razorpay Payment Settings</h3>

                    <div class="form-group">
                        <label for="razorpay_key_id">Razorpay Key ID</label>
                        <input type="text" name="razorpay_key_id" id="razorpay_key_id" value="{{ $settings['razorpay_key_id']->setting_value ?? '' }}" placeholder="rzp_test_xxxxxxxxxx">
                        <small style="color: #6b7280; font-size: 0.875rem;">Get this from Razorpay Dashboard → Settings → API Keys</small>
                    </div>

                    <div class="form-group">
                        <label for="razorpay_key_secret">Razorpay Key Secret</label>
                        <input type="password" name="razorpay_key_secret" id="razorpay_key_secret" value="{{ $settings['razorpay_key_secret']->setting_value ?? '' }}" placeholder="Enter Razorpay Key Secret">
                        <small style="color: #6b7280; font-size: 0.875rem;">Keep this secret and secure</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-create">💾 Save Settings</button>
        </div>
    </form>
</div>

<style>
    .image-preview {
        margin-top: 10px;
    }
    .image-preview img {
        max-width: 200px;
        max-height: 100px;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        padding: 5px;
        background-color: #f9fafb;
    }
    .logo-upload-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    .current-logo {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // Color picker functionality
    const colorInputs = ['primary_color', 'secondary_color', 'background_color', 'button_color'];
    colorInputs.forEach(colorId => {
        const colorInput = document.getElementById(colorId);
        const textInput = document.getElementById(colorId + '_text');

        if (colorInput && textInput) {
            colorInput.addEventListener('input', function() {
                textInput.value = this.value;
            });

            textInput.addEventListener('input', function() {
                if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(this.value)) {
                    colorInput.value = this.value;
                }
            });
        }
    });
});

// Image preview function
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.innerHTML = '<img src="' + e.target.result + '" alt="Preview" style="max-width: 200px; max-height: 100px; border: 1px solid #e5e7eb; border-radius: 4px; padding: 5px; background-color: #f9fafb; margin-top: 10px;">';
        };

        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = '';
    }
}

// Reset appearance defaults function
function resetAppearanceDefaults() {
    if (confirm('Are you sure you want to reset all appearance settings to their default values?')) {
        // Reset color values
        document.getElementById('primary_color').value = '#3b82f6';
        document.getElementById('primary_color_text').value = '#3b82f6';
        document.getElementById('secondary_color').value = '#10b981';
        document.getElementById('secondary_color_text').value = '#10b981';
        document.getElementById('background_color').value = '#f8fafc';
        document.getElementById('background_color_text').value = '#f8fafc';
        document.getElementById('button_color').value = '#3b82f6';
        document.getElementById('button_color_text').value = '#3b82f6';

        // Reset font values
        document.getElementById('heading_font').value = 'Arial';
        document.getElementById('body_font').value = 'Arial';
        document.getElementById('heading_font_size').value = '24';
        document.getElementById('body_font_size').value = '16';

        alert('Appearance settings have been reset to defaults. Don\'t forget to save your changes!');
    }
}
</script>
@endsection
