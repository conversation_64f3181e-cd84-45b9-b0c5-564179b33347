<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new appearance and branding settings with default values
        $newSettings = [
            // Branding settings
            'application_name' => 'Social Media Post Creator',
            'application_tagline' => 'Create stunning social media posts effortlessly',

            // Appearance settings - Colors
            'primary_color' => '#3b82f6',
            'secondary_color' => '#10b981',
            'background_color' => '#f8fafc',
            'button_color' => '#3b82f6',

            // Appearance settings - Typography
            'heading_font' => 'Arial',
            'body_font' => 'Arial',
            'heading_font_size' => '24',
            'body_font_size' => '16',
        ];

        foreach ($newSettings as $key => $value) {
            Setting::updateOrCreate(
                ['setting_key' => $key],
                [
                    'setting_value' => $value,
                    'comment' => 'Default appearance/branding setting'
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the new settings
        $settingsToRemove = [
            'application_name',
            'application_tagline',
            'primary_color',
            'secondary_color',
            'background_color',
            'button_color',
            'heading_font',
            'body_font',
            'heading_font_size',
            'body_font_size',
        ];

        Setting::whereIn('setting_key', $settingsToRemove)->delete();
    }
};
