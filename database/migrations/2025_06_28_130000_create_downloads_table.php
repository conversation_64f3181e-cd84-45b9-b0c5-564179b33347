<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('downloads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('business_id')->nullable()->constrained()->onDelete('set null');
            $table->string('template_type'); // 'poster' or 'frame'
            $table->unsignedBigInteger('template_id')->nullable();
            $table->json('design_data'); // Store the complete design configuration
            $table->string('file_path')->nullable(); // Path to generated image
            $table->string('file_name');
            $table->string('file_format', 10)->default('png'); // png, jpg, pdf
            $table->integer('file_size')->nullable(); // File size in bytes
            $table->string('download_type'); // 'free', 'subscription', 'paid'
            $table->decimal('cost', 8, 2)->default(0.00); // Cost if paid download
            $table->string('status')->default('pending'); // pending, processing, completed, failed
            $table->timestamp('completed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['template_type', 'template_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('downloads');
    }
};
