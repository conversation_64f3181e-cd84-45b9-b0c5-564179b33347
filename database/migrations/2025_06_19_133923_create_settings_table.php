<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id(); // BIGINT, PRIMARY KEY
            $table->string('setting_key')->unique(); // VARCHAR(255), UNIQUE
            $table->string('setting_value'); // VARCHAR(255)
            $table->text('comment')->nullable(); // TEXT
            $table->timestamps(); // created_at, updated_at
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
