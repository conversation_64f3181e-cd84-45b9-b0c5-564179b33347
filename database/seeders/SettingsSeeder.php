<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'setting_key' => 'subscription_enabled',
                'setting_value' => 'false',
                'comment' => 'Enable or disable subscription functionality'
            ],
            [
                'setting_key' => 'pay_per_download_enabled',
                'setting_value' => 'false',
                'comment' => 'Enable or disable pay per download functionality'
            ],
            [
                'setting_key' => 'per_download_cost',
                'setting_value' => '1.00',
                'comment' => 'Cost per download in INR'
            ],
            [
                'setting_key' => 'free_download_enabled',
                'setting_value' => 'true',
                'comment' => 'Enable or disable free downloads'
            ],
            [
                'setting_key' => 'google_client_id',
                'setting_value' => env('GOOGLE_CLIENT_ID', ''),
                'comment' => 'Google OAuth Client ID'
            ],
            [
                'setting_key' => 'google_client_secret',
                'setting_value' => env('GOOGLE_CLIENT_SECRET', ''),
                'comment' => 'Google OAuth Client Secret'
            ],
            [
                'setting_key' => 'google_redirect_url',
                'setting_value' => env('GOOGLE_REDIRECT_URL', ''),
                'comment' => 'Google OAuth Redirect URL'
            ],
            [
                'setting_key' => 'razorpay_key_id',
                'setting_value' => env('RAZORPAY_KEY_ID', ''),
                'comment' => 'Razorpay API Key ID'
            ],
            [
                'setting_key' => 'razorpay_key_secret',
                'setting_value' => env('RAZORPAY_KEY_SECRET', ''),
                'comment' => 'Razorpay API Key Secret'
            ],
            [
                'setting_key' => 'app_logo',
                'setting_value' => '',
                'comment' => 'Application logo URL'
            ],
            [
                'setting_key' => 'app_favicon',
                'setting_value' => '',
                'comment' => 'Application favicon URL'
            ],
            [
                'setting_key' => 'application_logo',
                'setting_value' => '',
                'comment' => 'Admin panel logo URL'
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['setting_key' => $setting['setting_key']],
                [
                    'setting_value' => $setting['setting_value'],
                    'comment' => $setting['comment']
                ]
            );
        }
    }
}
