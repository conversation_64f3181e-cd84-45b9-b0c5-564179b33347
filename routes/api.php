<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\SettingsController;
use App\Http\Controllers\Api\PlanController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\PosterGenerationController;
use App\Http\Controllers\Api\BusinessController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\PosterController;
use App\Http\Controllers\Api\FrameController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Enjoy building your API!
|
*/

// Public routes
Route::get('/settings', [SettingsController::class, 'index']);
Route::get('/plans', [PlanController::class, 'index']);
Route::post('/payment/create-order', [PaymentController::class, 'createOrder']);

// Categories routes (public)
Route::get('/categories', [CategoryController::class, 'index']);
Route::get('/categories/{category}', [CategoryController::class, 'show']);
Route::get('/categories/{category}/subcategories', [CategoryController::class, 'subcategories']);

// Posters routes (public)
Route::get('/posters', [PosterController::class, 'index']);
Route::get('/posters/{poster}', [PosterController::class, 'show']);
Route::get('/categories/{category}/posters', [PosterController::class, 'byCategory']);

// Frames routes (public)
Route::get('/frames', [FrameController::class, 'index']);
Route::get('/frames/{frame}', [FrameController::class, 'show']);
Route::get('/categories/{category}/frames', [FrameController::class, 'byCategory']);


// Protected routes - require authentication
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    Route::post('/payment/verify-signature', [PaymentController::class, 'verifySignature']);
    Route::post('/generate-poster', [PosterGenerationController::class, 'generatePoster'])->name('api.poster.generate');

    // Business management routes
    Route::apiResource('businesses', BusinessController::class);
    Route::post('/businesses/{business}/set-default', [BusinessController::class, 'setDefault']);
});
