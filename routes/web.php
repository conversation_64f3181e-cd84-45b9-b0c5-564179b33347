<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth; // Added Auth facade
use App\Http\Controllers\Auth\GoogleLoginController; // Added GoogleLoginController
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\PlanController; // Added PlanController import
use App\Http\Controllers\Admin\TransactionController; // Added TransactionController import
use App\Http\Controllers\Admin\PosterController; // Added PosterController import
use App\Http\Controllers\Admin\FrameController; // Added FrameController import
use App\Http\Controllers\Admin\CategoryController; // Added CategoryController import
use App\Http\Controllers\User\AuthController; // Added User AuthController import
use App\Http\Controllers\User\UserController; // Added User UserController import
use App\Http\Controllers\User\DesignController; // Added User DesignController import
use App\Http\Controllers\User\PaymentController as UserPaymentController; // Added User PaymentController import
use App\Http\Controllers\SitemapController; // Added SitemapController import
use App\Http\Controllers\PageController; // Added PageController import

// Default login route for Laravel auth middleware (must be defined early)
Route::get('/login', function () {
    return redirect()->route('user.login');
})->name('login');

// Root route
Route::get('/', function () {
    return redirect()->route('user.home');
});


// Admin Login Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin Login Form
    Route::get('login', function () {
        if (Auth::check()) {
            return redirect()->route('admin.dashboard');
        }
        return view('admin.login');
    })->name('login');

    // Admin Login Process
    Route::post('login', function (\Illuminate\Http\Request $request) {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($credentials, $request->boolean('remember'))) {
            $request->session()->regenerate();
            return redirect()->intended(route('admin.dashboard'));
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    })->name('login.process');

    // Admin Logout
    Route::post('logout', function (\Illuminate\Http\Request $request) {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('admin.login');
    })->name('logout');
});

// Protected Admin Routes
Route::prefix('admin')->name('admin.')->middleware('auth')->group(function () {
    // Admin Dashboard
    Route::get('/', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    Route::get('dashboard', function () {
        return view('admin.dashboard');
    })->name('dashboard');

    // Admin Settings Routes
    Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::post('settings', [SettingsController::class, 'store'])->name('settings.store');

    // Admin Plans Routes
    Route::resource('plans', PlanController::class)->except(['show'])->names('plans');

    // Admin Transactions Route
    Route::get('transactions', [TransactionController::class, 'index'])->name('transactions.index');

    // Admin Posters Routes
    Route::resource('posters', PosterController::class)->except(['show'])->names('posters');
    Route::patch('posters/{poster}/toggle-status', [PosterController::class, 'toggleStatus'])->name('posters.toggle-status');

    // Admin Frames Routes
    Route::resource('frames', FrameController::class)->except(['show'])->names('frames');
    Route::patch('frames/{frame}/toggle-status', [FrameController::class, 'toggleStatus'])->name('frames.toggle-status');

    // Admin Categories Routes
    Route::resource('categories', CategoryController::class)->except(['show'])->names('categories');
    Route::patch('categories/{category}/toggle-status', [CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::get('categories/subcategories', [CategoryController::class, 'getSubcategories'])->name('categories.subcategories');
});

// User Authentication Routes
Route::prefix('')->name('user.')->group(function () {
    // Guest routes
    Route::middleware('guest')->group(function () {
        Route::get('login', [AuthController::class, 'showLogin'])->name('login');
        Route::post('login', [AuthController::class, 'login'])->name('login.process');
        Route::get('forgot-password', [AuthController::class, 'showForgotPassword'])->name('password.request');
        Route::post('forgot-password', [AuthController::class, 'forgotPassword'])->name('password.email');
    });

    // Public routes (accessible to both guests and authenticated users)
    Route::get('home', [UserController::class, 'home'])->name('home');
    Route::get('categories', [UserController::class, 'categories'])->name('categories');
    Route::get('categories/{category}', [UserController::class, 'categoryShow'])->name('categories.show');

    // Authenticated routes
    Route::middleware('auth')->group(function () {
        Route::get('dashboard', [UserController::class, 'dashboard'])->name('dashboard');
        Route::get('profile', [UserController::class, 'profile'])->name('profile');
        Route::put('profile', [UserController::class, 'updateProfile'])->name('profile.update');
        Route::put('password', [UserController::class, 'updatePassword'])->name('password.update');
        Route::delete('account', [UserController::class, 'deleteAccount'])->name('account.delete');
        Route::get('downloads', [UserController::class, 'downloads'])->name('downloads');
        Route::get('downloads/{download}/file', [UserController::class, 'downloadFile'])->name('downloads.file');
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');

        // Design Editor routes
        Route::prefix('design')->name('design.')->group(function () {
            Route::get('create', [DesignController::class, 'create'])->name('create');
            Route::post('save', [DesignController::class, 'save'])->name('save');
            Route::post('download', [DesignController::class, 'download'])->name('download');
            Route::get('business/{business}', [DesignController::class, 'getBusiness'])->name('business');
        });

        // Payment routes
        Route::prefix('payment')->name('payment.')->group(function () {
            Route::get('plans', [UserPaymentController::class, 'showPlans'])->name('plans');
            Route::post('subscription/create', [UserPaymentController::class, 'createSubscriptionOrder'])->name('subscription.create');
            Route::post('subscription/verify', [UserPaymentController::class, 'verifySubscriptionPayment'])->name('subscription.verify');
            Route::post('download/create', [UserPaymentController::class, 'createDownloadOrder'])->name('download.create');
            Route::post('download/verify', [UserPaymentController::class, 'verifyDownloadPayment'])->name('download.verify');
            Route::get('download/check', [UserPaymentController::class, 'checkDownloadEligibility'])->name('download.check');
        });
    });
});

// Google OAuth Routes
Route::get('/auth/google', [GoogleLoginController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/callback/google', [GoogleLoginController::class, 'handleGoogleCallback'])->name('auth.google.callback');

// Debug route to check OAuth configuration
Route::get('/debug/google-config', function () {
    return response()->json([
        'google_client_id' => config('services.google.client_id'),
        'google_redirect_url' => config('services.google.redirect'),
        'app_url' => config('app.url'),
        'route_url' => route('auth.google.callback'),
        'current_url' => request()->getSchemeAndHttpHost(),
        'socialite_redirect_url' => \Laravel\Socialite\Facades\Socialite::driver('google')->getRedirectUrl(),
    ]);
});

// Test route to check if Google OAuth is working
Route::get('/test/google-auth', function () {
    try {
        // Just check if config is available
        return response()->json([
            'status' => 'success',
            'message' => 'Google OAuth configuration is available',
            'redirect_url' => config('services.google.redirect'),
            'client_id' => config('services.google.client_id'),
            'client_secret_set' => !empty(config('services.google.client_secret')),
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Google OAuth configuration error',
            'error' => $e->getMessage(),
        ], 500);
    }
});

// Simple Google OAuth redirect without extra processing
Route::get('/auth/google-simple', function () {
    try {
        $url = 'https://accounts.google.com/o/oauth2/auth?' . http_build_query([
            'client_id' => config('services.google.client_id'),
            'redirect_uri' => config('services.google.redirect'),
            'scope' => 'openid profile email',
            'response_type' => 'code',
            'state' => \Str::random(40),
        ]);

        return redirect($url);
    } catch (Exception $e) {
        return redirect()->route('login')->with('error', 'Google OAuth error: ' . $e->getMessage());
    }
})->name('auth.google.simple');

// Legacy logout route for compatibility
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// SEO Routes
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', [SitemapController::class, 'robots'])->name('robots');

// Static Pages
Route::get('/privacy-policy', [PageController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/terms-of-service', [PageController::class, 'termsOfService'])->name('terms-of-service');
Route::get('/about', [PageController::class, 'about'])->name('about');
Route::get('/contact', [PageController::class, 'contact'])->name('contact');
Route::get('/faq', [PageController::class, 'faq'])->name('faq');
