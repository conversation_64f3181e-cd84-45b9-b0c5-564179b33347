<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Frame;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FrameController extends Controller
{
    /**
     * Display a listing of active frames.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Frame::where('status', true);

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by subcategory
        if ($request->filled('subcategory_id')) {
            $query->where('subcategory_id', $request->subcategory_id);
        }

        // Filter by premium status
        if ($request->filled('is_premium')) {
            $query->where('is_premium', $request->boolean('is_premium'));
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $perPage = min($request->get('per_page', 12), 50); // Max 50 items per page
        $frames = $query->with(['category', 'subcategory'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        $data = $frames->getCollection()->map(function ($frame) {
            return [
                'id' => $frame->id,
                'name' => $frame->name,
                'image_url' => $frame->image_path ? Storage::url($frame->image_path) : null,
                'is_premium' => $frame->is_premium,
                'category' => $frame->category ? [
                    'id' => $frame->category->id,
                    'name' => $frame->category->name,
                    'slug' => $frame->category->slug,
                ] : null,
                'subcategory' => $frame->subcategory ? [
                    'id' => $frame->subcategory->id,
                    'name' => $frame->subcategory->name,
                    'slug' => $frame->subcategory->slug,
                ] : null,
                'created_at' => $frame->created_at,
            ];
        });

        return response()->json([
            'status' => 'success',
            'data' => $data,
            'pagination' => [
                'current_page' => $frames->currentPage(),
                'last_page' => $frames->lastPage(),
                'per_page' => $frames->perPage(),
                'total' => $frames->total(),
                'from' => $frames->firstItem(),
                'to' => $frames->lastItem(),
            ]
        ]);
    }

    /**
     * Display the specified frame.
     */
    public function show(Frame $frame): JsonResponse
    {
        if (!$frame->status) {
            return response()->json([
                'status' => 'error',
                'message' => 'Frame not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $frame->id,
                'name' => $frame->name,
                'image_url' => $frame->image_path ? Storage::url($frame->image_path) : null,
                'is_premium' => $frame->is_premium,
                'category' => $frame->category ? [
                    'id' => $frame->category->id,
                    'name' => $frame->category->name,
                    'slug' => $frame->category->slug,
                ] : null,
                'subcategory' => $frame->subcategory ? [
                    'id' => $frame->subcategory->id,
                    'name' => $frame->subcategory->name,
                    'slug' => $frame->subcategory->slug,
                ] : null,
                'created_at' => $frame->created_at,
            ]
        ]);
    }

    /**
     * Get frames by category.
     */
    public function byCategory(Request $request, Category $category): JsonResponse
    {
        if (!$category->status) {
            return response()->json([
                'status' => 'error',
                'message' => 'Category not found'
            ], 404);
        }

        $query = Frame::where('status', true);

        if ($category->isParent()) {
            // Get frames from all subcategories
            $subcategoryIds = $category->children()->active()->pluck('id');
            $query->whereIn('subcategory_id', $subcategoryIds);
        } else {
            // Get frames from this specific subcategory
            $query->where('subcategory_id', $category->id);
        }

        // Filter by premium status
        if ($request->filled('is_premium')) {
            $query->where('is_premium', $request->boolean('is_premium'));
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $perPage = min($request->get('per_page', 12), 50);
        $frames = $query->with(['category', 'subcategory'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        $data = $frames->getCollection()->map(function ($frame) {
            return [
                'id' => $frame->id,
                'name' => $frame->name,
                'image_url' => $frame->image_path ? Storage::url($frame->image_path) : null,
                'is_premium' => $frame->is_premium,
                'category' => $frame->category ? [
                    'id' => $frame->category->id,
                    'name' => $frame->category->name,
                    'slug' => $frame->category->slug,
                ] : null,
                'subcategory' => $frame->subcategory ? [
                    'id' => $frame->subcategory->id,
                    'name' => $frame->subcategory->name,
                    'slug' => $frame->subcategory->slug,
                ] : null,
                'created_at' => $frame->created_at,
            ];
        });

        return response()->json([
            'status' => 'success',
            'data' => $data,
            'pagination' => [
                'current_page' => $frames->currentPage(),
                'last_page' => $frames->lastPage(),
                'per_page' => $frames->perPage(),
                'total' => $frames->total(),
                'from' => $frames->firstItem(),
                'to' => $frames->lastItem(),
            ]
        ]);
    }
}
