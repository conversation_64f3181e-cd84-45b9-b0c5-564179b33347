<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Poster;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PosterController extends Controller
{
    /**
     * Display a listing of active posters.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Poster::where('status', true);

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by subcategory
        if ($request->filled('subcategory_id')) {
            $query->where('subcategory_id', $request->subcategory_id);
        }

        // Filter by premium status
        if ($request->filled('is_premium')) {
            $query->where('is_premium', $request->boolean('is_premium'));
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $perPage = min($request->get('per_page', 12), 50); // Max 50 items per page
        $posters = $query->with(['category', 'subcategory'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        $data = $posters->getCollection()->map(function ($poster) {
            return [
                'id' => $poster->id,
                'name' => $poster->name,
                'image_url' => $poster->image_path ? Storage::url($poster->image_path) : null,
                'is_premium' => $poster->is_premium,
                'category' => $poster->category ? [
                    'id' => $poster->category->id,
                    'name' => $poster->category->name,
                    'slug' => $poster->category->slug,
                ] : null,
                'subcategory' => $poster->subcategory ? [
                    'id' => $poster->subcategory->id,
                    'name' => $poster->subcategory->name,
                    'slug' => $poster->subcategory->slug,
                ] : null,
                'created_at' => $poster->created_at,
            ];
        });

        return response()->json([
            'status' => 'success',
            'data' => $data,
            'pagination' => [
                'current_page' => $posters->currentPage(),
                'last_page' => $posters->lastPage(),
                'per_page' => $posters->perPage(),
                'total' => $posters->total(),
                'from' => $posters->firstItem(),
                'to' => $posters->lastItem(),
            ]
        ]);
    }

    /**
     * Display the specified poster.
     */
    public function show(Poster $poster): JsonResponse
    {
        if (!$poster->status) {
            return response()->json([
                'status' => 'error',
                'message' => 'Poster not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $poster->id,
                'name' => $poster->name,
                'image_url' => $poster->image_path ? Storage::url($poster->image_path) : null,
                'is_premium' => $poster->is_premium,
                'category' => $poster->category ? [
                    'id' => $poster->category->id,
                    'name' => $poster->category->name,
                    'slug' => $poster->category->slug,
                ] : null,
                'subcategory' => $poster->subcategory ? [
                    'id' => $poster->subcategory->id,
                    'name' => $poster->subcategory->name,
                    'slug' => $poster->subcategory->slug,
                ] : null,
                'created_at' => $poster->created_at,
            ]
        ]);
    }

    /**
     * Get posters by category.
     */
    public function byCategory(Request $request, Category $category): JsonResponse
    {
        if (!$category->status) {
            return response()->json([
                'status' => 'error',
                'message' => 'Category not found'
            ], 404);
        }

        $query = Poster::where('status', true);

        if ($category->isParent()) {
            // Get posters from all subcategories
            $subcategoryIds = $category->children()->active()->pluck('id');
            $query->whereIn('subcategory_id', $subcategoryIds);
        } else {
            // Get posters from this specific subcategory
            $query->where('subcategory_id', $category->id);
        }

        // Filter by premium status
        if ($request->filled('is_premium')) {
            $query->where('is_premium', $request->boolean('is_premium'));
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $perPage = min($request->get('per_page', 12), 50);
        $posters = $query->with(['category', 'subcategory'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        $data = $posters->getCollection()->map(function ($poster) {
            return [
                'id' => $poster->id,
                'name' => $poster->name,
                'image_url' => $poster->image_path ? Storage::url($poster->image_path) : null,
                'is_premium' => $poster->is_premium,
                'category' => $poster->category ? [
                    'id' => $poster->category->id,
                    'name' => $poster->category->name,
                    'slug' => $poster->category->slug,
                ] : null,
                'subcategory' => $poster->subcategory ? [
                    'id' => $poster->subcategory->id,
                    'name' => $poster->subcategory->name,
                    'slug' => $poster->subcategory->slug,
                ] : null,
                'created_at' => $poster->created_at,
            ];
        });

        return response()->json([
            'status' => 'success',
            'data' => $data,
            'pagination' => [
                'current_page' => $posters->currentPage(),
                'last_page' => $posters->lastPage(),
                'per_page' => $posters->perPage(),
                'total' => $posters->total(),
                'from' => $posters->firstItem(),
                'to' => $posters->lastItem(),
            ]
        ]);
    }
}
