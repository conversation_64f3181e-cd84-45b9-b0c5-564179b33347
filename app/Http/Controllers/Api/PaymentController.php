<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Setting; // Import the Setting model
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Razorpay\Api\Api;
use Exception;
use Illuminate\Support\Facades\Log; // For logging errors
use Razorpay\Api\Errors\SignatureVerificationError; // Added this import

class PaymentController extends Controller
{
    protected $razorpay;

    public function __construct()
    {
        try {
            $razorpayConfig = Setting::getRazorpayConfig();
            if (!empty($razorpayConfig['key_id']) && !empty($razorpayConfig['key_secret'])) {
                $this->razorpay = new Api($razorpayConfig['key_id'], $razorpayConfig['key_secret']);
            } else {
                Log::error('Razorpay credentials not found in database settings');
                $this->razorpay = null;
            }
        } catch (Exception $e) {
            Log::error('Razorpay SDK instantiation error: ' . $e->getMessage());
            $this->razorpay = null;
        }
    }

    public function createOrder(Request $request)
    {
        if (!$this->razorpay) {
            return response()->json([
                'status' => 'error',
                'message' => 'Razorpay service is not configured properly.',
            ], 500);
        }

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:1', // Minimum 1 Rupee
            'purpose' => 'required|string|in:wallet,plan_purchase',
            'plan_id' => 'required_if:purpose,plan_purchase|exists:plans,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed.',
                'errors' => $validator->errors(),
            ], 422);
        }

        $amountInPaise = $request->input('amount') * 100;
        $currency = 'INR';
        $receiptId = uniqid('rcpt_'); // Generate a unique receipt ID

        $orderData = [
            'receipt' => $receiptId,
            'amount' => $amountInPaise,
            'currency' => $currency,
            'notes' => [
                'purpose' => $request->input('purpose'),
            ],
        ];

        if ($request->input('purpose') === 'plan_purchase') {
            $orderData['notes']['plan_id'] = $request->input('plan_id');
        }

        // Attempt to get authenticated user ID
        // This assumes API routes are protected by Sanctum or similar
        // and $request->user() will return the authenticated user.
        $userId = auth()->id(); // or $request->user()->id if using guard
        if ($userId) {
            $orderData['notes']['user_id'] = $userId;
        } else {
            // Handle case where user is not authenticated for the API call
            // For now, we'll allow creating order without user_id in notes,
            // but verification step will be crucial for linking.
            // Alternatively, return an authentication error here.
             Log::warning('Create Order API called without authenticated user.');
            // return response()->json(['status' => 'error', 'message' => 'User not authenticated.'], 401);
        }


        try {
            $razorpayOrder = $this->razorpay->order->create($orderData);

            return response()->json([
                'status' => 'success',
                'order_id' => $razorpayOrder->id,
                'amount' => $amountInPaise, // Amount is in paise
                'currency' => $currency,
                'notes' => $orderData['notes'] // Send back notes for client reference
            ]);
        } catch (Exception $e) {
            Log::error('Razorpay order creation failed: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error creating order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Verify the Razorpay payment signature.
     * This method assumes it's called after the client-side payment completion
     * and the route is protected by Sanctum to get the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifySignature(Request $request)
    {
        // Basic validation for required parameters from Razorpay
        $request->validate([
            'razorpay_payment_id' => 'required|string',
            'razorpay_order_id' => 'required|string', // Typically sent by client
            'razorpay_signature' => 'required|string', // Typically sent by client
        ]);

        // Assuming Sanctum authentication, $request->user() will give the authenticated user.
        $user = $request->user();
        if (!$user) {
            // This should ideally be handled by Sanctum middleware if route is protected
            return response()->json(['status' => 'error', 'message' => 'User not authenticated.'], 401);
        }
        $userId = $user->id;

        // The actual signature verification logic will be added in the next subtask.
        // For now, this block simulates the structure.
        try {
            // Placeholder for Utility::verifyPaymentSignature call
            // For now, just simulate success for testing the block
            $simulated_verification_success = true;

            if ($simulated_verification_success) {
                // Next step will be to replace simulation with actual call
                // And then handle actual successful verification logic
                return response()->json(['status' => 'temp_success', 'message' => 'Signature block reached, simulated success']);
            } else {
                // This else will be removed when actual verification is used
                throw new \Exception("Simulated verification failure");
            }

        } catch (SignatureVerificationError $e) {
            // Ensure use Razorpay\Api\Errors\SignatureVerificationError; is imported
            // Ensure use Illuminate\Support\Facades\Log; is imported
            Log::error('Razorpay Signature Verification Failed: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => 'Payment verification failed'], 400);
        }
        // Catching generic Exception for other issues, including the simulated failure
        catch (Exception $e) {
            Log::error('Error in verifySignature: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => 'An unexpected error occurred: ' . $e->getMessage()], 500);
        }
    }
}
