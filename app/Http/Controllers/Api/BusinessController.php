<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class BusinessController extends Controller
{
    /**
     * Display a listing of the user's businesses.
     */
    public function index(Request $request): JsonResponse
    {
        $businesses = $request->user()->businesses()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($business) {
                return [
                    'id' => $business->id,
                    'name' => $business->name,
                    'slogan' => $business->slogan,
                    'address' => $business->address,
                    'phone' => $business->phone,
                    'whatsapp' => $business->whatsapp,
                    'email' => $business->email,
                    'website' => $business->website,
                    'logo_url' => $business->logo_url,
                    'is_default' => $business->is_default,
                    'created_at' => $business->created_at,
                    'updated_at' => $business->updated_at,
                ];
            });

        return response()->json([
            'status' => 'success',
            'data' => $businesses
        ]);
    }

    /**
     * Store a newly created business.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'slogan' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20',
            'whatsapp' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_default' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['user_id'] = $request->user()->id;

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('business-logos', 'public');
            $data['logo_path'] = $logoPath;
        }

        $business = Business::create($data);

        return response()->json([
            'status' => 'success',
            'message' => 'Business created successfully',
            'data' => [
                'id' => $business->id,
                'name' => $business->name,
                'slogan' => $business->slogan,
                'address' => $business->address,
                'phone' => $business->phone,
                'whatsapp' => $business->whatsapp,
                'email' => $business->email,
                'website' => $business->website,
                'logo_url' => $business->logo_url,
                'is_default' => $business->is_default,
                'created_at' => $business->created_at,
                'updated_at' => $business->updated_at,
            ]
        ], 201);
    }

    /**
     * Display the specified business.
     */
    public function show(Request $request, Business $business): JsonResponse
    {
        // Ensure user can only access their own businesses
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Business not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $business->id,
                'name' => $business->name,
                'slogan' => $business->slogan,
                'address' => $business->address,
                'phone' => $business->phone,
                'whatsapp' => $business->whatsapp,
                'email' => $business->email,
                'website' => $business->website,
                'logo_url' => $business->logo_url,
                'is_default' => $business->is_default,
                'created_at' => $business->created_at,
                'updated_at' => $business->updated_at,
            ]
        ]);
    }

    /**
     * Update the specified business.
     */
    public function update(Request $request, Business $business): JsonResponse
    {
        // Ensure user can only update their own businesses
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Business not found'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'slogan' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20',
            'whatsapp' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_default' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($business->logo_path) {
                Storage::disk('public')->delete($business->logo_path);
            }
            
            $logoPath = $request->file('logo')->store('business-logos', 'public');
            $data['logo_path'] = $logoPath;
        }

        $business->update($data);

        return response()->json([
            'status' => 'success',
            'message' => 'Business updated successfully',
            'data' => [
                'id' => $business->id,
                'name' => $business->name,
                'slogan' => $business->slogan,
                'address' => $business->address,
                'phone' => $business->phone,
                'whatsapp' => $business->whatsapp,
                'email' => $business->email,
                'website' => $business->website,
                'logo_url' => $business->logo_url,
                'is_default' => $business->is_default,
                'created_at' => $business->created_at,
                'updated_at' => $business->updated_at,
            ]
        ]);
    }

    /**
     * Remove the specified business.
     */
    public function destroy(Request $request, Business $business): JsonResponse
    {
        // Ensure user can only delete their own businesses
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Business not found'
            ], 404);
        }

        // Prevent deletion if it's the only business
        $businessCount = $request->user()->businesses()->count();
        if ($businessCount <= 1) {
            return response()->json([
                'status' => 'error',
                'message' => 'Cannot delete the only business profile'
            ], 422);
        }

        $business->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Business deleted successfully'
        ]);
    }

    /**
     * Set a business as default.
     */
    public function setDefault(Request $request, Business $business): JsonResponse
    {
        // Ensure user can only modify their own businesses
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Business not found'
            ], 404);
        }

        $business->update(['is_default' => true]);

        return response()->json([
            'status' => 'success',
            'message' => 'Default business updated successfully'
        ]);
    }
}
