<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Display a listing of active categories.
     */
    public function index(): JsonResponse
    {
        $categories = Category::active()
            ->parents()
            ->with(['children' => function ($query) {
                $query->active()->orderBy('sort_order')->orderBy('name');
            }])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'icon' => $category->icon,
                    'sort_order' => $category->sort_order,
                    'children' => $category->children->map(function ($child) {
                        return [
                            'id' => $child->id,
                            'name' => $child->name,
                            'slug' => $child->slug,
                            'description' => $child->description,
                            'icon' => $child->icon,
                            'sort_order' => $child->sort_order,
                        ];
                    })
                ];
            });

        return response()->json([
            'status' => 'success',
            'data' => $categories
        ]);
    }

    /**
     * Display the specified category with its subcategories.
     */
    public function show(Category $category): JsonResponse
    {
        if (!$category->status) {
            return response()->json([
                'status' => 'error',
                'message' => 'Category not found'
            ], 404);
        }

        $categoryData = [
            'id' => $category->id,
            'name' => $category->name,
            'slug' => $category->slug,
            'description' => $category->description,
            'icon' => $category->icon,
            'sort_order' => $category->sort_order,
            'is_parent' => $category->isParent(),
        ];

        if ($category->isParent()) {
            $categoryData['children'] = $category->children()
                ->active()
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get()
                ->map(function ($child) {
                    return [
                        'id' => $child->id,
                        'name' => $child->name,
                        'slug' => $child->slug,
                        'description' => $child->description,
                        'icon' => $child->icon,
                        'sort_order' => $child->sort_order,
                    ];
                });
        } else {
            $categoryData['parent'] = $category->parent ? [
                'id' => $category->parent->id,
                'name' => $category->parent->name,
                'slug' => $category->parent->slug,
            ] : null;
        }

        return response()->json([
            'status' => 'success',
            'data' => $categoryData
        ]);
    }

    /**
     * Get subcategories for a specific parent category.
     */
    public function subcategories(Request $request, Category $category): JsonResponse
    {
        if (!$category->status || !$category->isParent()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Category not found or is not a parent category'
            ], 404);
        }

        $subcategories = $category->children()
            ->active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get()
            ->map(function ($child) {
                return [
                    'id' => $child->id,
                    'name' => $child->name,
                    'slug' => $child->slug,
                    'description' => $child->description,
                    'icon' => $child->icon,
                    'sort_order' => $child->sort_order,
                ];
            });

        return response()->json([
            'status' => 'success',
            'data' => $subcategories
        ]);
    }
}
