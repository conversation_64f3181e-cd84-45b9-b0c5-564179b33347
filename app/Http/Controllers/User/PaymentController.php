<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\Setting;
use App\Models\Subscription;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Razorpay\Api\Api;

class PaymentController extends Controller
{
    private $razorpay;
    
    public function __construct()
    {
        $this->razorpay = new Api(
            Setting::get('razorpay_key_id'),
            Setting::get('razorpay_key_secret')
        );
    }
    
    /**
     * Show subscription plans.
     */
    public function showPlans()
    {
        $plans = Plan::where('status', true)
            ->orderBy('price', 'asc')
            ->get();
            
        $settings = [
            'subscription_enabled' => Setting::get('subscription_enabled', 'false') === 'true',
            'pay_per_download_enabled' => Setting::get('pay_per_download_enabled', 'false') === 'true',
            'free_download_enabled' => Setting::get('free_download_enabled', 'true') === 'true',
            'per_download_cost' => Setting::get('per_download_cost', '1.00'),
        ];
        
        // Get user's current subscription
        $currentSubscription = Auth::user()->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->with('plan')
            ->first();
            
        return view('user.payment.plans', compact('plans', 'settings', 'currentSubscription'));
    }
    
    /**
     * Create subscription order.
     */
    public function createSubscriptionOrder(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
        ]);
        
        $plan = Plan::findOrFail($request->plan_id);
        $user = Auth::user();
        
        // Check if subscription is enabled
        if (Setting::get('subscription_enabled', 'false') !== 'true') {
            return response()->json([
                'status' => 'error',
                'message' => 'Subscriptions are currently disabled'
            ], 400);
        }
        
        try {
            // Create Razorpay order
            $orderData = [
                'receipt' => 'sub_' . $user->id . '_' . time(),
                'amount' => $plan->price * 100, // Amount in paise
                'currency' => 'INR',
                'notes' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'type' => 'subscription'
                ]
            ];
            
            $razorpayOrder = $this->razorpay->order->create($orderData);
            
            return response()->json([
                'status' => 'success',
                'order_id' => $razorpayOrder['id'],
                'amount' => $razorpayOrder['amount'],
                'currency' => $razorpayOrder['currency'],
                'key' => Setting::get('razorpay_key_id'),
                'plan' => [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'price' => $plan->price,
                    'duration' => $plan->duration_days
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create payment order: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Create pay-per-download order.
     */
    public function createDownloadOrder(Request $request)
    {
        $request->validate([
            'design_data' => 'required|json',
            'template_id' => 'nullable|integer',
            'template_type' => 'required|in:poster,frame',
        ]);
        
        $user = Auth::user();
        $downloadCost = (float) Setting::get('per_download_cost', '1.00');
        
        // Check if pay-per-download is enabled
        if (Setting::get('pay_per_download_enabled', 'false') !== 'true') {
            return response()->json([
                'status' => 'error',
                'message' => 'Pay-per-download is currently disabled'
            ], 400);
        }
        
        try {
            // Create Razorpay order
            $orderData = [
                'receipt' => 'download_' . $user->id . '_' . time(),
                'amount' => $downloadCost * 100, // Amount in paise
                'currency' => 'INR',
                'notes' => [
                    'user_id' => $user->id,
                    'type' => 'download',
                    'template_id' => $request->template_id,
                    'template_type' => $request->template_type
                ]
            ];
            
            $razorpayOrder = $this->razorpay->order->create($orderData);
            
            return response()->json([
                'status' => 'success',
                'order_id' => $razorpayOrder['id'],
                'amount' => $razorpayOrder['amount'],
                'currency' => $razorpayOrder['currency'],
                'key' => Setting::get('razorpay_key_id'),
                'download_cost' => $downloadCost
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create payment order: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Verify payment and process subscription.
     */
    public function verifySubscriptionPayment(Request $request)
    {
        $request->validate([
            'razorpay_order_id' => 'required',
            'razorpay_payment_id' => 'required',
            'razorpay_signature' => 'required',
            'plan_id' => 'required|exists:plans,id',
        ]);
        
        try {
            // Verify signature
            $attributes = [
                'razorpay_order_id' => $request->razorpay_order_id,
                'razorpay_payment_id' => $request->razorpay_payment_id,
                'razorpay_signature' => $request->razorpay_signature
            ];
            
            $this->razorpay->utility->verifyPaymentSignature($attributes);
            
            $plan = Plan::findOrFail($request->plan_id);
            $user = Auth::user();
            
            DB::transaction(function () use ($user, $plan, $request) {
                // Deactivate existing subscriptions
                $user->subscriptions()
                    ->where('status', 'active')
                    ->update(['status' => 'cancelled']);
                
                // Create new subscription
                $subscription = Subscription::create([
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'start_date' => now(),
                    'end_date' => now()->addDays($plan->duration_days),
                    'status' => 'active',
                    'razorpay_order_id' => $request->razorpay_order_id,
                    'razorpay_payment_id' => $request->razorpay_payment_id,
                ]);
                
                // Create transaction record
                Transaction::create([
                    'user_id' => $user->id,
                    'type' => 'debit',
                    'amount' => $plan->price,
                    'description' => 'Subscription: ' . $plan->name,
                    'status' => 'completed',
                    'razorpay_payment_id' => $request->razorpay_payment_id,
                ]);
            });
            
            return response()->json([
                'status' => 'success',
                'message' => 'Subscription activated successfully!',
                'redirect' => route('user.dashboard')
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment verification failed: ' . $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Verify payment and process download.
     */
    public function verifyDownloadPayment(Request $request)
    {
        $request->validate([
            'razorpay_order_id' => 'required',
            'razorpay_payment_id' => 'required',
            'razorpay_signature' => 'required',
            'design_data' => 'required|json',
        ]);
        
        try {
            // Verify signature
            $attributes = [
                'razorpay_order_id' => $request->razorpay_order_id,
                'razorpay_payment_id' => $request->razorpay_payment_id,
                'razorpay_signature' => $request->razorpay_signature
            ];
            
            $this->razorpay->utility->verifyPaymentSignature($attributes);
            
            $user = Auth::user();
            $downloadCost = (float) Setting::get('per_download_cost', '1.00');
            
            DB::transaction(function () use ($user, $downloadCost, $request) {
                // Create transaction record
                Transaction::create([
                    'user_id' => $user->id,
                    'type' => 'debit',
                    'amount' => $downloadCost,
                    'description' => 'Download Payment',
                    'status' => 'completed',
                    'razorpay_payment_id' => $request->razorpay_payment_id,
                ]);
            });
            
            // Here you would generate and return the download
            // For now, return success message
            return response()->json([
                'status' => 'success',
                'message' => 'Payment successful! Download will start shortly.',
                'download_url' => null // Will be implemented in download system
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Payment verification failed: ' . $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Check download eligibility.
     */
    public function checkDownloadEligibility(Request $request)
    {
        $user = Auth::user();
        $settings = [
            'subscription_enabled' => Setting::get('subscription_enabled', 'false') === 'true',
            'pay_per_download_enabled' => Setting::get('pay_per_download_enabled', 'false') === 'true',
            'free_download_enabled' => Setting::get('free_download_enabled', 'true') === 'true',
        ];
        
        // Check if user has active subscription
        $hasActiveSubscription = $user->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->exists();
        
        $eligibility = [
            'can_download_free' => $settings['free_download_enabled'],
            'can_download_subscription' => $settings['subscription_enabled'] && $hasActiveSubscription,
            'can_download_paid' => $settings['pay_per_download_enabled'],
            'has_active_subscription' => $hasActiveSubscription,
            'download_cost' => Setting::get('per_download_cost', '1.00'),
        ];
        
        return response()->json([
            'status' => 'success',
            'eligibility' => $eligibility
        ]);
    }
}
