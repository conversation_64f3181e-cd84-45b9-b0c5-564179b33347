<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Poster;
use App\Models\Frame;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    /**
     * Show the user home page.
     */
    public function home()
    {
        // Get featured categories
        $categories = Category::active()
            ->parents()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->take(8)
            ->get();

        // Get recent posters
        $recentPosters = Poster::where('status', true)
            ->with(['category', 'subcategory'])
            ->orderBy('created_at', 'desc')
            ->take(12)
            ->get();

        // Get app settings for payment model
        $settings = [
            'subscription_enabled' => Setting::get('subscription_enabled', 'false') === 'true',
            'pay_per_download_enabled' => Setting::get('pay_per_download_enabled', 'false') === 'true',
            'free_download_enabled' => Setting::get('free_download_enabled', 'true') === 'true',
            'per_download_cost' => Setting::get('per_download_cost', '1.00'),
        ];

        return view('user.home', compact('categories', 'recentPosters', 'settings'));
    }

    /**
     * Show the user dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        // Get user's businesses
        $businesses = $user->businesses()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get user's subscription status
        $activeSubscription = $user->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->with('plan')
            ->first();

        // Get user's wallet balance
        $wallet = $user->wallet;

        // Get recent transactions
        $recentTransactions = $user->transactions()
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Get download statistics
        $totalDownloads = $user->downloads()->completed()->count();
        $thisMonthDownloads = $user->downloads()
            ->completed()
            ->whereMonth('completed_at', now()->month)
            ->whereYear('completed_at', now()->year)
            ->count();

        $stats = [
            'total_downloads' => $totalDownloads,
            'this_month_downloads' => $thisMonthDownloads,
            'total_businesses' => $businesses->count(),
            'wallet_balance' => $wallet ? $wallet->balance : 0,
        ];

        return view('user.dashboard', compact(
            'user', 
            'businesses', 
            'activeSubscription', 
            'wallet', 
            'recentTransactions', 
            'stats'
        ));
    }

    /**
     * Show categories page.
     */
    public function categories()
    {
        $categories = Category::active()
            ->parents()
            ->with(['children' => function ($query) {
                $query->active()->orderBy('sort_order')->orderBy('name');
            }])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // Add template counts for each category
        foreach ($categories as $category) {
            if ($category->hasChildren()) {
                // For parent categories, count templates from all subcategories and direct assignments
                $subcategoryIds = $category->children->pluck('id');
                $category->template_count = Poster::where('status', true)
                    ->where(function ($query) use ($category, $subcategoryIds) {
                        $query->whereIn('subcategory_id', $subcategoryIds)
                              ->orWhere('category_id', $category->id);
                    })
                    ->count();

                // Add template counts for subcategories
                foreach ($category->children as $subcategory) {
                    $subcategory->template_count = Poster::where('status', true)
                        ->where(function ($query) use ($subcategory) {
                            $query->where('subcategory_id', $subcategory->id)
                                  ->orWhere('category_id', $subcategory->id);
                        })
                        ->count();
                }
            } else {
                // For categories without subcategories, count direct templates
                $category->template_count = Poster::where('status', true)
                    ->where(function ($query) use ($category) {
                        $query->where('subcategory_id', $category->id)
                              ->orWhere('category_id', $category->id);
                    })
                    ->count();
            }
        }

        return view('user.categories', compact('categories'));
    }

    /**
     * Show category details with posters and frames.
     */
    public function categoryShow(Request $request, Category $category)
    {
        if (!$category->status) {
            abort(404);
        }

        // Get posters for this category with filtering
        $postersQuery = Poster::where('status', true);

        if ($category->isParent()) {
            // Get posters from all subcategories and direct category assignments
            $subcategoryIds = $category->children()->active()->pluck('id');
            $postersQuery->where(function ($query) use ($category, $subcategoryIds) {
                $query->whereIn('subcategory_id', $subcategoryIds)
                      ->orWhere('category_id', $category->id);
            });
        } else {
            // Get posters from this specific subcategory or category
            $postersQuery->where(function ($query) use ($category) {
                $query->where('subcategory_id', $category->id)
                      ->orWhere('category_id', $category->id);
            });
        }

        // Apply filters
        if ($request->filled('search')) {
            $postersQuery->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->filled('is_premium')) {
            $postersQuery->where('is_premium', $request->boolean('is_premium'));
        }

        // Apply sorting
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'oldest':
                $postersQuery->orderBy('created_at', 'asc');
                break;
            case 'name':
                $postersQuery->orderBy('name', 'asc');
                break;
            case 'newest':
            default:
                $postersQuery->orderBy('created_at', 'desc');
                break;
        }

        $posters = $postersQuery->with(['category', 'subcategory'])
            ->paginate(12)
            ->appends($request->query());

        // Get frames for this category
        $framesQuery = Frame::where('status', true);

        if ($category->isParent()) {
            // Get frames from all subcategories and direct category assignments
            $subcategoryIds = $category->children()->active()->pluck('id');
            $framesQuery->where(function ($query) use ($category, $subcategoryIds) {
                $query->whereIn('subcategory_id', $subcategoryIds)
                      ->orWhere('category_id', $category->id);
            });
        } else {
            // Get frames from this specific subcategory or category
            $framesQuery->where(function ($query) use ($category) {
                $query->where('subcategory_id', $category->id)
                      ->orWhere('category_id', $category->id);
            });
        }

        $frames = $framesQuery->orderBy('created_at', 'desc')
            ->take(12)
            ->get();

        // Get subcategories if this is a parent category
        $subcategories = $category->isParent()
            ? $category->children()->active()->orderBy('sort_order')->orderBy('name')->get()
            : collect();

        return view('user.category-show', compact('category', 'posters', 'frames', 'subcategories'));
    }

    /**
     * Show user profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        
        return view('user.profile', compact('user'));
    }

    /**
     * Update user profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $user->update([
            'name' => $request->name,
        ]);

        return back()->with('success', 'Profile updated successfully.');
    }

    /**
     * Update user password.
     */
    public function updatePassword(Request $request)
    {
        $user = Auth::user();

        // Check if user signed up with Google
        if ($user->google_id) {
            return back()->withErrors(['password' => 'You signed up with Google. Password changes are managed through your Google account.']);
        }

        $request->validate([
            'current_password' => 'required|string',
            'password' => ['required', 'string', 'confirmed', Password::min(8)],
        ]);

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return back()->with('success', 'Password updated successfully.');
    }

    /**
     * Delete user account.
     */
    public function deleteAccount(Request $request)
    {
        $user = Auth::user();

        // Log out the user first
        Auth::logout();

        // Invalidate the session
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        // Delete the user account (this will cascade delete related data due to foreign key constraints)
        $user->delete();

        return redirect()->route('user.login')
            ->with('success', 'Your account has been successfully deleted.');
    }

    /**
     * Show user's download history.
     */
    public function downloads(Request $request)
    {
        $user = Auth::user();

        $downloads = $user->downloads()
            ->with(['business'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('user.downloads', compact('downloads'));
    }

    /**
     * Download a previously generated file.
     */
    public function downloadFile(Download $download)
    {
        $user = Auth::user();

        // Ensure user can only download their own files
        if ($download->user_id !== $user->id) {
            abort(404);
        }

        // Check if file exists and download is completed
        if ($download->status !== 'completed' || !$download->file_path) {
            return back()->with('error', 'File not available for download');
        }

        if (!Storage::exists($download->file_path)) {
            return back()->with('error', 'File not found');
        }

        return Storage::download($download->file_path, $download->file_name . '.' . $download->file_format);
    }
}
