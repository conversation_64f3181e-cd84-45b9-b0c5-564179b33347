<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Poster;
use App\Models\Frame;
use Illuminate\Http\Response;

class SitemapController extends Controller
{
    /**
     * Generate XML sitemap.
     */
    public function index()
    {
        $urls = collect();
        
        // Add static pages
        $urls->push([
            'url' => route('user.home'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'daily',
            'priority' => '1.0'
        ]);
        
        $urls->push([
            'url' => route('user.categories'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'weekly',
            'priority' => '0.8'
        ]);
        
        $urls->push([
            'url' => route('user.payment.plans'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'monthly',
            'priority' => '0.7'
        ]);
        
        $urls->push([
            'url' => route('user.login'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'monthly',
            'priority' => '0.5'
        ]);
        
        $urls->push([
            'url' => route('user.register'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'monthly',
            'priority' => '0.5'
        ]);
        
        // Add category pages
        $categories = Category::active()->get();
        foreach ($categories as $category) {
            $urls->push([
                'url' => route('user.categories.show', $category),
                'lastmod' => $category->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ]);
        }
        
        $xml = $this->generateSitemapXml($urls);
        
        return response($xml, 200, [
            'Content-Type' => 'application/xml'
        ]);
    }
    
    /**
     * Generate robots.txt file.
     */
    public function robots()
    {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Disallow: /admin/\n";
        $content .= "Disallow: /api/\n";
        $content .= "Disallow: /design/\n";
        $content .= "Disallow: /payment/\n";
        $content .= "Disallow: /dashboard\n";
        $content .= "Disallow: /profile\n";
        $content .= "Disallow: /downloads\n";
        $content .= "\n";
        $content .= "Sitemap: " . route('sitemap') . "\n";
        
        return response($content, 200, [
            'Content-Type' => 'text/plain'
        ]);
    }
    
    /**
     * Generate XML sitemap content.
     */
    private function generateSitemapXml($urls)
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($url['url']) . '</loc>' . "\n";
            $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }
        
        $xml .= '</urlset>';
        
        return $xml;
    }
}
