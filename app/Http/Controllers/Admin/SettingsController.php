<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting; // Make sure to import the Setting model
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Fetch all settings, keyed by their 'setting_key' for easy access in the view
        $settings = Setting::all()->keyBy('setting_key');

        // Define default values for required settings if they are not in the database
        $defaultSettings = [
            'subscription_enabled' => 'false', // Storing as string 'true'/'false' or '1'/'0'
            'pay_per_download_enabled' => 'false',
            'per_download_cost' => '1.00',
            'free_download_enabled' => 'true',
            'google_client_id' => '',
            'google_client_secret' => '',
            'google_redirect_url' => '',
            'razorpay_key_id' => '',
            'razorpay_key_secret' => '',
            'app_logo' => '',
            'app_favicon' => '',
            'application_logo' => '',
            // Branding settings
            'application_name' => 'Social Media Post Creator',
            'application_tagline' => 'Create stunning social media posts effortlessly',
            // Appearance settings - Colors
            'primary_color' => '#3b82f6',
            'secondary_color' => '#10b981',
            'background_color' => '#f8fafc',
            'button_color' => '#3b82f6',
            // Appearance settings - Typography
            'heading_font' => 'Arial',
            'body_font' => 'Arial',
            'heading_font_size' => '24',
            'body_font_size' => '16',
        ];

        // Ensure all default settings are present in the $settings collection passed to the view
        foreach ($defaultSettings as $key => $value) {
            if (!isset($settings[$key])) {
                // Create a new Setting model instance for defaults not in DB
                // This doesn't save it, just makes it available to the view
                $setting = new Setting();
                $setting->setting_key = $key;
                $setting->setting_value = $value;
                $settings[$key] = $setting;
            }
        }

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Store a newly created or updated resource in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            // Logo file uploads
            'app_logo_file' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'app_favicon_file' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,ico|max:1024',
            'application_logo_file' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',

            // Branding fields
            'application_name' => 'nullable|string|max:255',
            'application_tagline' => 'nullable|string|max:500',

            // Color fields
            'primary_color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'secondary_color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'background_color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'button_color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',

            // Typography fields
            'heading_font' => 'nullable|string|max:100',
            'body_font' => 'nullable|string|max:100',
            'heading_font_size' => 'nullable|integer|min:10|max:72',
            'body_font_size' => 'nullable|integer|min:8|max:32',
        ]);

        // Handle file uploads for logos
        $logoFields = ['app_logo', 'app_favicon', 'application_logo'];
        foreach ($logoFields as $logoField) {
            $fileField = $logoField . '_file';
            if ($request->hasFile($fileField)) {
                $file = $request->file($fileField);

                if ($file->isValid()) {
                    // Delete old logo if it exists
                    $oldSetting = Setting::where('setting_key', $logoField)->first();
                    if ($oldSetting && $oldSetting->setting_value && Storage::disk('public')->exists($oldSetting->setting_value)) {
                        Storage::disk('public')->delete($oldSetting->setting_value);
                    }

                    // Generate unique filename
                    $filename = $logoField . '_' . time() . '.' . $file->getClientOriginalExtension();

                    // Store the file
                    $filePath = $file->storeAs('logos', $filename, 'public');

                    // Update the setting
                    Setting::updateOrCreate(
                        ['setting_key' => $logoField],
                        [
                            'setting_value' => $filePath,
                            'comment' => 'Logo file upload'
                        ]
                    );
                }
            }
        }

        $input = $request->except(['_token', 'app_logo_file', 'app_favicon_file', 'application_logo_file']);

        foreach ($input as $key => $value) {
            // Handle boolean values from checkboxes/toggles explicitly
            // HTML forms don't send unchecked checkbox values, so default to 'false' or 0
            if (in_array($key, ['subscription_enabled', 'pay_per_download_enabled', 'free_download_enabled'])) {
                $value = $request->has($key) ? 'true' : 'false';
            }

            Setting::updateOrCreate(
                ['setting_key' => $key],
                [
                    'setting_value' => $value,
                    'comment' => 'Auto-generated setting'
                ]
            );
        }

        // Ensure default settings exist after store operation, especially if form doesn't submit all fields
        $defaultSettings = [
            'subscription_enabled' => $request->has('subscription_enabled') ? 'true' : 'false',
            'pay_per_download_enabled' => $request->has('pay_per_download_enabled') ? 'true' : 'false',
            'per_download_cost' => $request->input('per_download_cost', '1.00'), // Keep existing or default
            'free_download_enabled' => $request->has('free_download_enabled') ? 'true' : 'true', // Default true if not submitted
            'google_client_id' => $request->input('google_client_id', ''),
            'google_client_secret' => $request->input('google_client_secret', ''),
            'google_redirect_url' => $request->input('google_redirect_url', ''),
            'razorpay_key_id' => $request->input('razorpay_key_id', ''),
            'razorpay_key_secret' => $request->input('razorpay_key_secret', ''),
            // Note: Logo settings are handled separately via file uploads above
            'application_name' => $request->input('application_name', 'Social Media Post Creator'),
            'application_tagline' => $request->input('application_tagline', 'Create stunning social media posts effortlessly'),
            'primary_color' => $request->input('primary_color', '#3b82f6'),
            'secondary_color' => $request->input('secondary_color', '#10b981'),
            'background_color' => $request->input('background_color', '#f8fafc'),
            'button_color' => $request->input('button_color', '#3b82f6'),
            'heading_font' => $request->input('heading_font', 'Arial'),
            'body_font' => $request->input('body_font', 'Arial'),
            'heading_font_size' => $request->input('heading_font_size', '24'),
            'body_font_size' => $request->input('body_font_size', '16'),
        ];

        foreach ($defaultSettings as $key => $value) {
             Setting::updateOrCreate(
                ['setting_key' => $key],
                [
                    'setting_value' => $value,
                    'comment' => 'Default application setting'
                ]
            );
        }


        return redirect()->route('admin.settings.index')->with('success', 'Settings updated successfully.');
    }
}
