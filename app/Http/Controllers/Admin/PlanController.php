<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\Setting; // Import the Setting model
use Illuminate\Http\Request;
use Razorpay\Api\Api; // Import Razorpay API
use Exception; // Import base Exception for catching errors

class PlanController extends Controller
{
    protected $razorpay;

    public function __construct()
    {
        // Initialize Razorpay API client using database settings
        try {
            $razorpayConfig = Setting::getRazorpayConfig();
            if (!empty($razorpayConfig['key_id']) && !empty($razorpayConfig['key_secret'])) {
                $this->razorpay = new Api($razorpayConfig['key_id'], $razorpayConfig['key_secret']);
            } else {
                $this->razorpay = null;
            }
        } catch (Exception $e) {
            // Log error or handle if keys are missing, but don't break constructor
            $this->razorpay = null;
        }
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $plans = Plan::orderBy('created_at', 'desc')->get();
        return view('admin.plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.plans.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1', // e.g., 1, 7, 30, 365
        ]);

        if (!$this->razorpay) {
            return redirect()->back()->withInput()->with('error', 'Razorpay API client could not be initialized. Check API keys.');
        }

        $razorpay_plan_id = null;

        try {
            // Debug: Check Razorpay configuration
            $razorpayConfig = Setting::getRazorpayConfig();
            \Log::info('Razorpay Config Debug', [
                'key_id_set' => !empty($razorpayConfig['key_id']),
                'key_secret_set' => !empty($razorpayConfig['key_secret']),
                'key_id_length' => strlen($razorpayConfig['key_id'] ?? ''),
                'key_secret_length' => strlen($razorpayConfig['key_secret'] ?? ''),
            ]);

            // For now, skip Razorpay plan creation and just create local plan
            // This is a temporary fix to avoid the API error
            \Log::info('Skipping Razorpay plan creation due to API issues');
            $razorpay_plan_id = 'local_plan_' . time(); // Temporary ID

            // TODO: Re-enable Razorpay plan creation once API issues are resolved
            /*
            // Determine the appropriate period and interval based on duration_days
            $durationDays = (int)$request->input('duration_days');
            $period = 'monthly';
            $interval = 1;

            if ($durationDays <= 7) {
                $period = 'daily';
                $interval = $durationDays;
            } elseif ($durationDays <= 365) {
                $period = 'monthly';
                $interval = max(1, round($durationDays / 30));
            } else {
                $period = 'yearly';
                $interval = max(1, round($durationDays / 365));
            }

            $planData = [
                'period' => $period,
                'interval' => $interval,
                'item' => [
                    'name' => $request->input('name'),
                    'description' => $request->input('description'),
                    'amount' => (int)($request->input('price') * 100), // Amount in paise
                    'currency' => 'INR',
                ],
                'notes' => [
                    'duration_days' => $durationDays,
                    'created_by' => 'admin_panel'
                ]
            ];

            $razorpayPlan = $this->razorpay->plan->create($planData);
            $razorpay_plan_id = $razorpayPlan->id;
            */

        } catch (Exception $e) {
            \Log::error('Razorpay Plan Creation Failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->back()->withInput()->with('error', 'Failed to create plan on Razorpay: ' . $e->getMessage());
        }

        Plan::create([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'price' => $request->input('price'),
            'duration_days' => $request->input('duration_days'),
            'razorpay_plan_id' => $razorpay_plan_id,
            'status' => true, // Default to active
        ]);

        return redirect()->route('admin.plans.index')->with('success', 'Plan created successfully. Note: Razorpay integration temporarily disabled - Plan ID: ' . $razorpay_plan_id);
    }

    /**
     * Display the specified resource.
     * (This method is typically not used for admin CRUD if `except(['show'])` is used in routes)
     */
    public function show(Plan $plan)
    {
        // return view('admin.plans.show', compact('plan')); // If a show view exists
        return redirect()->route('admin.plans.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Plan $plan)
    {
        return view('admin.plans.edit', compact('plan'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Plan $plan)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'status' => 'required|boolean',
        ]);

        // Note: Updating Razorpay plan details (price, interval, period) is generally not allowed.
        // You usually create a new plan on Razorpay and associate it.
        // For this task, we are primarily updating the local database record.
        // If the user changes details that would require a new Razorpay plan,
        // that logic would be more complex (e.g., creating a new plan, migrating subscribers).
        // Here, we'll assume razorpay_plan_id does not change once set, or it's managed manually if a new one is needed.

        $plan->update([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'price' => $request->input('price'),
            'duration_days' => $request->input('duration_days'),
            'status' => $request->input('status'),
        ]);

        return redirect()->route('admin.plans.index')->with('success', 'Plan updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     * (Actually deactivates by setting status to 0)
     */
    public function destroy(Plan $plan)
    {
        // Deleting plans on Razorpay might not be possible or advisable if subscriptions are attached.
        // For this task, we just deactivate the local record.
        try {
            $plan->update(['status' => false]);
            return redirect()->route('admin.plans.index')->with('success', 'Plan deactivated successfully.');
        } catch (Exception $e) {
            return redirect()->route('admin.plans.index')->with('error', 'Failed to deactivate plan: ' . $e->getMessage());
        }
    }
}
