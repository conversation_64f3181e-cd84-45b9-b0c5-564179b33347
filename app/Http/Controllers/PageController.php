<?php

namespace App\Http\Controllers;

use App\Services\SeoService;
use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * Show privacy policy page.
     */
    public function privacyPolicy()
    {
        $seoData = [
            'title' => 'Privacy Policy',
            'description' => 'Learn how we collect, use, and protect your personal information when you use our social media post creator.',
            'keywords' => 'privacy policy, data protection, personal information, social media creator',
        ];
        
        return view('pages.privacy-policy', compact('seoData'));
    }
    
    /**
     * Show terms of service page.
     */
    public function termsOfService()
    {
        $seoData = [
            'title' => 'Terms of Service',
            'description' => 'Read our terms and conditions for using our social media post creation platform.',
            'keywords' => 'terms of service, terms and conditions, user agreement, social media creator',
        ];
        
        return view('pages.terms-of-service', compact('seoData'));
    }
    
    /**
     * Show about us page.
     */
    public function about()
    {
        $seoData = [
            'title' => 'About Us',
            'description' => 'Learn about our mission to help businesses create stunning social media content effortlessly.',
            'keywords' => 'about us, company, mission, social media design, business tools',
        ];
        
        return view('pages.about', compact('seoData'));
    }
    
    /**
     * Show contact page.
     */
    public function contact()
    {
        $seoData = [
            'title' => 'Contact Us',
            'description' => 'Get in touch with our support team for help with your social media design needs.',
            'keywords' => 'contact us, support, help, customer service, social media creator',
        ];
        
        return view('pages.contact', compact('seoData'));
    }
    
    /**
     * Show FAQ page.
     */
    public function faq()
    {
        $seoData = [
            'title' => 'Frequently Asked Questions',
            'description' => 'Find answers to common questions about our social media post creation platform.',
            'keywords' => 'FAQ, frequently asked questions, help, support, social media creator',
        ];
        
        $faqs = [
            [
                'question' => 'How do I create my first design?',
                'answer' => 'Simply browse our categories, select a template you like, and use our drag-and-drop editor to customize it with your business information.'
            ],
            [
                'question' => 'Can I use the designs for commercial purposes?',
                'answer' => 'Yes! All designs created with our platform can be used for commercial purposes, including marketing and advertising for your business.'
            ],
            [
                'question' => 'What file formats can I download?',
                'answer' => 'You can download your designs in PNG and JPG formats. Premium users also get access to high-resolution files.'
            ],
            [
                'question' => 'How does the subscription work?',
                'answer' => 'Our subscription plans give you unlimited access to premium templates and features. You can cancel anytime and your subscription will remain active until the end of your billing period.'
            ],
            [
                'question' => 'Can I add my own logo and branding?',
                'answer' => 'Absolutely! You can create business profiles with your logo, colors, and contact information that will be automatically applied to your designs.'
            ],
            [
                'question' => 'Is there a free plan available?',
                'answer' => 'Yes, we offer free downloads with basic features. You can also choose pay-per-download or subscribe for unlimited access to premium features.'
            ],
            [
                'question' => 'How do I get support?',
                'answer' => 'You can contact our support team through the contact form, and premium subscribers get priority support with faster response times.'
            ],
            [
                'question' => 'Can I cancel my subscription?',
                'answer' => 'Yes, you can cancel your subscription at any time from your dashboard. Your access will continue until the end of your current billing period.'
            ]
        ];
        
        return view('pages.faq', compact('seoData', 'faqs'));
    }
}
