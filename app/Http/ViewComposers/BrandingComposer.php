<?php

namespace App\Http\ViewComposers;

use Illuminate\View\View;
use App\Models\Setting;

class BrandingComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $branding = Setting::getBrandingConfig();
        $appearance = Setting::getAppearanceConfig();
        
        $view->with('branding', $branding);
        $view->with('appearance', $appearance);
    }
}
