<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Str;

class SeoService
{
    /**
     * Generate SEO-friendly title.
     */
    public static function generateTitle(string $pageTitle, ?string $suffix = null): string
    {
        $appName = Setting::getBrandingConfig()['application_name'] ?? 'Social Media Post Creator';
        $title = $pageTitle;
        
        if ($suffix) {
            $title .= ' - ' . $suffix;
        }
        
        $title .= ' | ' . $appName;
        
        // Ensure title is not too long (recommended max 60 characters)
        if (strlen($title) > 60) {
            $title = Str::limit($pageTitle, 40) . ' | ' . $appName;
        }
        
        return $title;
    }
    
    /**
     * Generate SEO-friendly description.
     */
    public static function generateDescription(string $description, int $maxLength = 160): string
    {
        return Str::limit($description, $maxLength);
    }
    
    /**
     * Generate keywords from content.
     */
    public static function generateKeywords(array $keywords): string
    {
        // Remove duplicates and empty values
        $keywords = array_filter(array_unique($keywords));
        
        // Limit to 10 keywords max
        $keywords = array_slice($keywords, 0, 10);
        
        return implode(', ', $keywords);
    }
    
    /**
     * Generate Open Graph data.
     */
    public static function generateOpenGraphData(array $data): array
    {
        $branding = Setting::getBrandingConfig();
        
        return [
            'title' => $data['title'] ?? $branding['application_name'] ?? 'Social Media Post Creator',
            'description' => $data['description'] ?? $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly',
            'image' => $data['image'] ?? ($branding['app_logo'] ? asset('storage/' . $branding['app_logo']) : null),
            'url' => $data['url'] ?? url()->current(),
            'type' => $data['type'] ?? 'website',
            'site_name' => $branding['application_name'] ?? 'Social Media Post Creator',
        ];
    }
    
    /**
     * Generate Twitter Card data.
     */
    public static function generateTwitterCardData(array $data): array
    {
        $branding = Setting::getBrandingConfig();
        
        return [
            'card' => $data['card'] ?? 'summary_large_image',
            'title' => $data['title'] ?? $branding['application_name'] ?? 'Social Media Post Creator',
            'description' => $data['description'] ?? $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly',
            'image' => $data['image'] ?? ($branding['app_logo'] ? asset('storage/' . $branding['app_logo']) : null),
        ];
    }
    
    /**
     * Generate JSON-LD structured data.
     */
    public static function generateJsonLd(string $type, array $data): array
    {
        $branding = Setting::getBrandingConfig();
        
        $baseData = [
            '@context' => 'https://schema.org',
            '@type' => $type,
        ];
        
        switch ($type) {
            case 'WebApplication':
                return array_merge($baseData, [
                    'name' => $branding['application_name'] ?? 'Social Media Post Creator',
                    'description' => $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly',
                    'url' => url('/'),
                    'applicationCategory' => 'DesignApplication',
                    'operatingSystem' => 'Web Browser',
                    'offers' => [
                        '@type' => 'Offer',
                        'price' => '0',
                        'priceCurrency' => 'INR',
                        'availability' => 'https://schema.org/InStock'
                    ],
                    'creator' => [
                        '@type' => 'Organization',
                        'name' => $branding['application_name'] ?? 'Social Media Post Creator'
                    ]
                ], $data);
                
            case 'Organization':
                return array_merge($baseData, [
                    'name' => $branding['application_name'] ?? 'Social Media Post Creator',
                    'url' => url('/'),
                    'logo' => $branding['app_logo'] ? asset('storage/' . $branding['app_logo']) : null,
                    'description' => $branding['application_tagline'] ?? 'Create stunning social media posts effortlessly',
                ], $data);
                
            case 'WebPage':
                return array_merge($baseData, [
                    'name' => $data['name'] ?? 'Page',
                    'description' => $data['description'] ?? '',
                    'url' => url()->current(),
                    'isPartOf' => [
                        '@type' => 'WebSite',
                        'name' => $branding['application_name'] ?? 'Social Media Post Creator',
                        'url' => url('/')
                    ]
                ], $data);
                
            case 'BreadcrumbList':
                return array_merge($baseData, [
                    'itemListElement' => $data['items'] ?? []
                ]);
                
            default:
                return array_merge($baseData, $data);
        }
    }
    
    /**
     * Generate breadcrumb JSON-LD.
     */
    public static function generateBreadcrumbJsonLd(array $breadcrumbs): array
    {
        $items = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url'] ?? null
            ];
        }
        
        return self::generateJsonLd('BreadcrumbList', ['items' => $items]);
    }
    
    /**
     * Generate canonical URL.
     */
    public static function generateCanonicalUrl(?string $url = null): string
    {
        return $url ?? url()->current();
    }
    
    /**
     * Generate robots meta content.
     */
    public static function generateRobotsMeta(bool $index = true, bool $follow = true, array $additional = []): string
    {
        $robots = [];
        
        $robots[] = $index ? 'index' : 'noindex';
        $robots[] = $follow ? 'follow' : 'nofollow';
        
        // Add additional directives
        foreach ($additional as $directive) {
            if (in_array($directive, ['noarchive', 'nosnippet', 'noimageindex', 'notranslate'])) {
                $robots[] = $directive;
            }
        }
        
        return implode(',', $robots);
    }
    
    /**
     * Generate hreflang tags for multi-language support.
     */
    public static function generateHreflangTags(array $languages): array
    {
        $tags = [];
        
        foreach ($languages as $lang => $url) {
            $tags[] = [
                'rel' => 'alternate',
                'hreflang' => $lang,
                'href' => $url
            ];
        }
        
        return $tags;
    }
    
    /**
     * Optimize image alt text for SEO.
     */
    public static function optimizeImageAlt(string $filename, ?string $context = null): string
    {
        // Remove file extension and clean filename
        $alt = pathinfo($filename, PATHINFO_FILENAME);
        $alt = str_replace(['-', '_'], ' ', $alt);
        $alt = ucwords($alt);
        
        if ($context) {
            $alt = $context . ' - ' . $alt;
        }
        
        return $alt;
    }
    
    /**
     * Generate meta tags array for easy rendering.
     */
    public static function generateMetaTags(array $data): array
    {
        $tags = [];
        
        // Basic meta tags
        if (isset($data['title'])) {
            $tags[] = ['name' => 'title', 'content' => $data['title']];
        }
        
        if (isset($data['description'])) {
            $tags[] = ['name' => 'description', 'content' => $data['description']];
        }
        
        if (isset($data['keywords'])) {
            $tags[] = ['name' => 'keywords', 'content' => $data['keywords']];
        }
        
        if (isset($data['robots'])) {
            $tags[] = ['name' => 'robots', 'content' => $data['robots']];
        }
        
        // Open Graph tags
        $ogData = self::generateOpenGraphData($data);
        foreach ($ogData as $property => $content) {
            if ($content) {
                $tags[] = ['property' => 'og:' . $property, 'content' => $content];
            }
        }
        
        // Twitter Card tags
        $twitterData = self::generateTwitterCardData($data);
        foreach ($twitterData as $name => $content) {
            if ($content) {
                $tags[] = ['name' => 'twitter:' . $name, 'content' => $content];
            }
        }
        
        return $tags;
    }
}
