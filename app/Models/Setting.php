<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'setting_key',
        'setting_value',
        'comment',
    ];

    /**
     * Get a setting value by key with fallback to environment variable
     */
    public static function get($key, $default = null)
    {
        $setting = static::where('setting_key', $key)->first();

        if ($setting && !empty($setting->setting_value)) {
            return $setting->setting_value;
        }

        // Fallback to environment variable
        $envKey = strtoupper($key);
        return env($envKey, $default);
    }

    /**
     * Get Razorpay configuration from database or environment
     */
    public static function getRazorpayConfig()
    {
        return [
            'key_id' => static::get('razorpay_key_id', env('RAZORPAY_KEY_ID')),
            'key_secret' => static::get('razorpay_key_secret', env('RAZORPAY_KEY_SECRET')),
        ];
    }

    /**
     * Get Google OAuth configuration from database or environment
     */
    public static function getGoogleConfig()
    {
        return [
            'client_id' => static::get('google_client_id', env('GOOGLE_CLIENT_ID')),
            'client_secret' => static::get('google_client_secret', env('GOOGLE_CLIENT_SECRET')),
            'redirect' => static::get('google_redirect_url', env('GOOGLE_REDIRECT_URL')),
        ];
    }

    /**
     * Get branding configuration from database
     */
    public static function getBrandingConfig()
    {
        return [
            'application_name' => static::get('application_name', 'Social Media Post Creator'),
            'application_tagline' => static::get('application_tagline', 'Create stunning social media posts effortlessly'),
            'app_logo' => static::get('app_logo', ''),
            'app_favicon' => static::get('app_favicon', ''),
            'application_logo' => static::get('application_logo', ''),
        ];
    }

    /**
     * Get appearance configuration from database
     */
    public static function getAppearanceConfig()
    {
        return [
            'primary_color' => static::get('primary_color', '#3b82f6'),
            'secondary_color' => static::get('secondary_color', '#10b981'),
            'background_color' => static::get('background_color', '#f8fafc'),
            'button_color' => static::get('button_color', '#3b82f6'),
            'heading_font' => static::get('heading_font', 'Arial'),
            'body_font' => static::get('body_font', 'Arial'),
            'heading_font_size' => static::get('heading_font_size', '24'),
            'body_font_size' => static::get('body_font_size', '16'),
        ];
    }

    /**
     * Get logo URL with fallback
     */
    public static function getLogoUrl($logoType = 'app_logo')
    {
        $logoPath = static::get($logoType, '');
        if ($logoPath && \Storage::disk('public')->exists($logoPath)) {
            return \Storage::url($logoPath);
        }
        return '';
    }
}
