<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Business extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'slogan',
        'address',
        'phone',
        'whatsapp',
        'email',
        'website',
        'logo_path',
        'is_default',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns the business.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the logo URL attribute.
     */
    public function getLogoUrlAttribute(): ?string
    {
        if ($this->logo_path) {
            return Storage::url($this->logo_path);
        }
        return null;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Ensure only one default business per user
        static::creating(function ($business) {
            if ($business->is_default) {
                static::where('user_id', $business->user_id)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }
        });

        static::updating(function ($business) {
            if ($business->is_default && $business->isDirty('is_default')) {
                static::where('user_id', $business->user_id)
                    ->where('id', '!=', $business->id)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }
        });

        // Delete logo file when business is deleted
        static::deleting(function ($business) {
            if ($business->logo_path) {
                Storage::delete($business->logo_path);
            }
        });
    }

    /**
     * Scope to get businesses for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get default business for a user.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }
}
